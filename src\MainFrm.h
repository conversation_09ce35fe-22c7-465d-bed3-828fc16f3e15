#pragma once

class CMainFrame : public CFrameWnd
{
	
protected: // create from serialization only
	CMainFrame();
	DECLARE_DYNCREATE(CMainFrame)

// Attributes
public:

// Operations
public:

// Overrides
public:
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);

// Implementation
public:
	virtual ~CMainFrame();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:  // control bar embedded members
	CToolBar          m_wndToolBar;
	CStatusBar        m_wndStatusBar;

// Generated message map functions
protected:
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnMenuAccounts();
	afx_msg void OnMenuJournalEntries();
	afx_msg void OnMenuLedger();
	afx_msg void OnMenuTrialBalance();
	afx_msg void OnMenuIncomeStatement();
	afx_msg void OnMenuBalanceSheet();
	afx_msg void OnMenuSettings();
	afx_msg void OnMenuExit();
	DECLARE_MESSAGE_MAP()
}; 