#include "stdafx.h"
#include "AccountsTreeDlg.h"
#include "Utils/ArabicSupport.h"
#include "Utils/NumberFormat.h"

IMPLEMENT_DYNAMIC(CAccountsTreeDlg, CDialog)

CAccountsTreeDlg::CAccountsTreeDlg(CWnd* pParent /*=nullptr*/)
	: CDialog(CAccountsTreeDlg::IDD, pParent)
	, m_selectedAccount(nullptr)
{
}

CAccountsTreeDlg::~CAccountsTreeDlg()
{
}

void CAccountsTreeDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_TREE_ACCOUNTS, m_treeAccounts);
	DDX_Control(pDX, IDC_BTN_ADD_ACCOUNT, m_btnAddAccount);
	DDX_Control(pDX, IDC_BTN_EDIT_ACCOUNT, m_btnEditAccount);
	DDX_Control(pDX, IDC_BTN_DELETE_ACCOUNT, m_btnDeleteAccount);
	DDX_Control(pDX, IDC_EDIT_ACCOUNT_CODE, m_editAccountCode);
	DDX_Control(pDX, IDC_EDIT_ACCOUNT_NAME, m_editAccountName);
	DDX_Control(pDX, IDC_COMBO_ACCOUNT_TYPE, m_comboAccountType);
	DDX_Control(pDX, IDC_EDIT_ACCOUNT_DESCRIPTION, m_editAccountDescription);
}

BEGIN_MESSAGE_MAP(CAccountsTreeDlg, CDialog)
	ON_BN_CLICKED(IDC_BTN_ADD_ACCOUNT, &CAccountsTreeDlg::OnBnClickedAddAccount)
	ON_BN_CLICKED(IDC_BTN_EDIT_ACCOUNT, &CAccountsTreeDlg::OnBnClickedEditAccount)
	ON_BN_CLICKED(IDC_BTN_DELETE_ACCOUNT, &CAccountsTreeDlg::OnBnClickedDeleteAccount)
	ON_NOTIFY(TVN_SELCHANGED, IDC_TREE_ACCOUNTS, &CAccountsTreeDlg::OnTvnSelchangedTreeAccounts)
	ON_NOTIFY(TVN_ITEMEXPANDED, IDC_TREE_ACCOUNTS, &CAccountsTreeDlg::OnTvnItemexpandedTreeAccounts)
END_MESSAGE_MAP()

BOOL CAccountsTreeDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// Set RTL layout
	CArabicSupport::SetRTLWindow(this);

	// Set Arabic fonts
	CArabicSupport::SetArabicFont(this, _T("Arial"));

	// Set window title
	SetWindowText(_T("شجرة الحسابات"));

	// Set button texts
	m_btnAddAccount.SetWindowText(_T("إضافة حساب"));
	m_btnEditAccount.SetWindowText(_T("تعديل"));
	m_btnDeleteAccount.SetWindowText(_T("حذف"));

	// Load account types
	LoadAccountTypes();

	// Load sample data for testing
	LoadSampleData();

	// Populate tree
	PopulateTree();

	// Disable edit/delete buttons initially
	m_btnEditAccount.EnableWindow(FALSE);
	m_btnDeleteAccount.EnableWindow(FALSE);

	return TRUE;
}

void CAccountsTreeDlg::LoadAccountTypes()
{
	m_comboAccountType.AddString(_T("أصول"));
	m_comboAccountType.AddString(_T("خصوم"));
	m_comboAccountType.AddString(_T("حقوق ملكية"));
	m_comboAccountType.AddString(_T("إيرادات"));
	m_comboAccountType.AddString(_T("مصروفات"));
}

void CAccountsTreeDlg::LoadSampleData()
{
	// Clear existing data
	m_accounts.clear();

	// Add sample accounts
	m_accounts.push_back(CAccount(1, 0, _T("1000"), _T("الأصول"), _T("Assets"), AccountType::Asset));
	m_accounts.push_back(CAccount(2, 1, _T("1100"), _T("الأصول المتداولة"), _T("Current Assets"), AccountType::Asset));
	m_accounts.push_back(CAccount(3, 2, _T("1110"), _T("النقد وما في حكمه"), _T("Cash and Cash Equivalents"), AccountType::Asset));
	m_accounts.push_back(CAccount(4, 2, _T("1120"), _T("الذمم المدينة"), _T("Accounts Receivable"), AccountType::Asset));
	m_accounts.push_back(CAccount(5, 2, _T("1130"), _T("المخزون"), _T("Inventory"), AccountType::Asset));
	
	m_accounts.push_back(CAccount(6, 0, _T("2000"), _T("الخصوم"), _T("Liabilities"), AccountType::Liability));
	m_accounts.push_back(CAccount(7, 6, _T("2100"), _T("الخصوم المتداولة"), _T("Current Liabilities"), AccountType::Liability));
	m_accounts.push_back(CAccount(8, 7, _T("2110"), _T("الذمم الدائنة"), _T("Accounts Payable"), AccountType::Liability));
	
	m_accounts.push_back(CAccount(9, 0, _T("3000"), _T("حقوق الملكية"), _T("Equity"), AccountType::Equity));
	m_accounts.push_back(CAccount(10, 9, _T("3100"), _T("رأس المال"), _T("Capital"), AccountType::Equity));
	
	m_accounts.push_back(CAccount(11, 0, _T("4000"), _T("الإيرادات"), _T("Revenues"), AccountType::Revenue));
	m_accounts.push_back(CAccount(12, 11, _T("4100"), _T("إيرادات المبيعات"), _T("Sales Revenue"), AccountType::Revenue));
	
	m_accounts.push_back(CAccount(13, 0, _T("5000"), _T("المصروفات"), _T("Expenses"), AccountType::Expense));
	m_accounts.push_back(CAccount(14, 13, _T("5100"), _T("مصروفات البيع"), _T("Selling Expenses"), AccountType::Expense));
}

void CAccountsTreeDlg::PopulateTree()
{
	m_treeAccounts.DeleteAllItems();

	// Add root accounts (parent_id = 0)
	for (const auto& account : m_accounts)
	{
		if (account.GetParentId() == 0)
		{
			AddAccountToTree(account);
		}
	}
}

void CAccountsTreeDlg::AddAccountToTree(const CAccount& account, HTREEITEM hParent)
{
	CString displayText;
	displayText.Format(_T("%s - %s"), account.GetCode(), account.GetNameAr());

	HTREEITEM hItem = m_treeAccounts.InsertItem(displayText, hParent);
	
	// Store account ID in item data
	m_treeAccounts.SetItemData(hItem, account.GetId());

	// Add children
	for (const auto& child : m_accounts)
	{
		if (child.GetParentId() == account.GetId())
		{
			AddAccountToTree(child, hItem);
		}
	}
}

void CAccountsTreeDlg::OnBnClickedAddAccount()
{
	ClearControls();
	
	// Enable controls for editing
	m_editAccountCode.EnableWindow(TRUE);
	m_editAccountName.EnableWindow(TRUE);
	m_comboAccountType.EnableWindow(TRUE);
	m_editAccountDescription.EnableWindow(TRUE);
	
	// Set focus to code field
	m_editAccountCode.SetFocus();
}

void CAccountsTreeDlg::OnBnClickedEditAccount()
{
	if (!m_selectedAccount)
		return;

	// Load account data into controls
	m_editAccountCode.SetWindowText(m_selectedAccount->GetCode());
	m_editAccountName.SetWindowText(m_selectedAccount->GetNameAr());
	m_editAccountDescription.SetWindowText(m_selectedAccount->GetDescription());
	
	// Set account type
	int typeIndex = static_cast<int>(m_selectedAccount->GetType());
	m_comboAccountType.SetCurSel(typeIndex);
}

void CAccountsTreeDlg::OnBnClickedDeleteAccount()
{
	if (!m_selectedAccount)
		return;

	CString accountName = m_selectedAccount->GetNameAr();
	if (ConfirmDelete(accountName))
	{
		// Remove from vector
		auto it = std::find_if(m_accounts.begin(), m_accounts.end(),
			[this](const CAccount& acc) { return acc.GetId() == m_selectedAccount->GetId(); });
		
		if (it != m_accounts.end())
		{
			m_accounts.erase(it);
			RefreshTree();
			ClearControls();
			ShowInfo(_T("تم حذف الحساب بنجاح"));
		}
	}
}

void CAccountsTreeDlg::OnTvnSelchangedTreeAccounts(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMTREEVIEW pNMTreeView = reinterpret_cast<LPNMTREEVIEW>(pNMHDR);
	
	HTREEITEM hSelected = pNMTreeView->itemNew.hItem;
	if (hSelected)
	{
		DWORD_PTR itemData = m_treeAccounts.GetItemData(hSelected);
		int accountId = static_cast<int>(itemData);
		
		// Find account
		auto it = std::find_if(m_accounts.begin(), m_accounts.end(),
			[accountId](const CAccount& acc) { return acc.GetId() == accountId; });
		
		if (it != m_accounts.end())
		{
			m_selectedAccount = &(*it);
			UpdateControls();
		}
	}
	
	*pResult = 0;
}

void CAccountsTreeDlg::OnTvnItemexpandedTreeAccounts(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMTREEVIEW pNMTreeView = reinterpret_cast<LPNMTREEVIEW>(pNMHDR);
	*pResult = 0;
}

void CAccountsTreeDlg::UpdateControls()
{
	if (m_selectedAccount)
	{
		m_btnEditAccount.EnableWindow(TRUE);
		m_btnDeleteAccount.EnableWindow(TRUE);
		
		// Display account info
		m_editAccountCode.SetWindowText(m_selectedAccount->GetCode());
		m_editAccountName.SetWindowText(m_selectedAccount->GetNameAr());
		m_editAccountDescription.SetWindowText(m_selectedAccount->GetDescription());
		
		int typeIndex = static_cast<int>(m_selectedAccount->GetType());
		m_comboAccountType.SetCurSel(typeIndex);
	}
	else
	{
		m_btnEditAccount.EnableWindow(FALSE);
		m_btnDeleteAccount.EnableWindow(FALSE);
		ClearControls();
	}
}

void CAccountsTreeDlg::ClearControls()
{
	m_editAccountCode.SetWindowText(_T(""));
	m_editAccountName.SetWindowText(_T(""));
	m_editAccountDescription.SetWindowText(_T(""));
	m_comboAccountType.SetCurSel(-1);
}

void CAccountsTreeDlg::RefreshTree()
{
	PopulateTree();
}

bool CAccountsTreeDlg::ConfirmDelete(const CString& accountName)
{
	CString message;
	message.Format(_T("هل أنت متأكد من حذف الحساب: %s؟"), accountName);
	
	int result = AfxMessageBox(message, MB_YESNO | MB_ICONQUESTION);
	return (result == IDYES);
}

void CAccountsTreeDlg::ShowError(const CString& message)
{
	AfxMessageBox(message, MB_OK | MB_ICONERROR);
}

void CAccountsTreeDlg::ShowInfo(const CString& message)
{
	AfxMessageBox(message, MB_OK | MB_ICONINFORMATION);
} 