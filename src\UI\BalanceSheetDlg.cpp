#include "stdafx.h"
#include "BalanceSheetDlg.h"
#include "Utils/ArabicSupport.h"
#include "Utils/NumberFormat.h"

IMPLEMENT_DYNAMIC(CBalanceSheetDlg, CDialog)

CBalanceSheetDlg::CBalanceSheetDlg(CWnd* pParent /*=nullptr*/)
	: CDialog(CBalanceSheetDlg::IDD, pParent)
{
}

CBalanceSheetDlg::~CBalanceSheetDlg()
{
}

void CBalanceSheetDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST_REPORT_DATA, m_listReportData);
	DDX_Control(pDX, IDC_BTN_PRINT, m_btnPrint);
	DDX_Control(pDX, IDC_BTN_EXPORT, m_btnExport);
	DDX_Control(pDX, IDC_DATE_FROM, m_dateAsOf);
	DDX_Control(pDX, IDC_BTN_GENERATE_REPORT, m_btnGenerateReport);
}

BEGIN_MESSAGE_MAP(CBalanceSheetDlg, CDialog)
END_MESSAGE_MAP()

BOOL CBalanceSheetDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// Set RTL layout
	CArabicSupport::SetRTLWindow(this);

	// Set Arabic fonts
	CArabicSupport::SetArabicFont(this, _T("Arial"));

	// Set window title
	SetWindowText(_T("الميزانية العمومية"));

	// Initialize controls
	InitializeControls();

	// Load sample data
	LoadSampleData();

	return TRUE;
}

void CBalanceSheetDlg::InitializeControls()
{
	// Set button texts
	m_btnPrint.SetWindowText(_T("طباعة"));
	m_btnExport.SetWindowText(_T("تصدير"));
	m_btnGenerateReport.SetWindowText(_T("توليد التقرير"));

	// Initialize list control
	m_listReportData.InsertColumn(0, _T("البند"), LVCFMT_LEFT, 300);
	m_listReportData.InsertColumn(1, _T("المبلغ"), LVCFMT_RIGHT, 150);

	// Set date to current date
	CTime now = CTime::GetCurrentTime();
	m_dateAsOf.SetTime(&now);
}

void CBalanceSheetDlg::LoadSampleData()
{
	// Add sample balance sheet data
	int index = 0;

	// Assets section
	m_listReportData.InsertItem(index, _T("الأصول:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("  الأصول المتداولة:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("    النقد وما في حكمه"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(50000.00));
	index++;

	m_listReportData.InsertItem(index, _T("    الذمم المدينة"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(25000.00));
	index++;

	m_listReportData.InsertItem(index, _T("    المخزون"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(15000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  مجموع الأصول المتداولة"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(90000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  الأصول الثابتة:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("    المباني"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(200000.00));
	index++;

	m_listReportData.InsertItem(index, _T("    الآلات والمعدات"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(50000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  مجموع الأصول الثابتة"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(250000.00));
	index++;

	m_listReportData.InsertItem(index, _T("مجموع الأصول"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(340000.00));
	index++;

	// Empty row
	m_listReportData.InsertItem(index, _T(""));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	// Liabilities section
	m_listReportData.InsertItem(index, _T("الخصوم:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("  الخصوم المتداولة:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("    الذمم الدائنة"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(20000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  مجموع الخصوم المتداولة"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(20000.00));
	index++;

	m_listReportData.InsertItem(index, _T("مجموع الخصوم"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(20000.00));
	index++;

	// Empty row
	m_listReportData.InsertItem(index, _T(""));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	// Equity section
	m_listReportData.InsertItem(index, _T("حقوق الملكية:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("  رأس المال"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(200000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  الأرباح المحتجزة"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(120000.00));
	index++;

	m_listReportData.InsertItem(index, _T("مجموع حقوق الملكية"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(320000.00));
	index++;

	// Empty row
	m_listReportData.InsertItem(index, _T(""));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	// Total
	m_listReportData.InsertItem(index, _T("مجموع الخصوم وحقوق الملكية"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(340000.00));
}

void CBalanceSheetDlg::GenerateReport()
{
	// TODO: Implement report generation from database
	AfxMessageBox(_T("سيتم تنفيذ توليد التقرير من قاعدة البيانات قريباً"));
} 