#pragma once

#include <afxwin.h>
#include <vector>

enum class AccountType
{
    Asset = 0,      // أصول
    Liability = 1,  // خصوم
    Equity = 2,     // حقوق ملكية
    Revenue = 3,    // إيرادات
    Expense = 4     // مصروفات
};

class CAccount
{
private:
    int m_id;
    int m_parentId;
    CString m_code;
    CString m_nameAr;
    CString m_nameEn;
    AccountType m_type;
    CString m_description;
    bool m_isActive;
    CTime m_createdAt;
    CTime m_updatedAt;
    
    // الحسابات الفرعية
    std::vector<CAccount> m_children;

public:
    CAccount();
    CAccount(int id, int parentId, const CString& code, const CString& nameAr, 
             const CString& nameEn, AccountType type, const CString& description = _T(""));
    virtual ~CAccount();

    // Getters
    int GetId() const { return m_id; }
    int GetParentId() const { return m_parentId; }
    CString GetCode() const { return m_code; }
    CString GetNameAr() const { return m_nameAr; }
    CString GetNameEn() const { return m_nameEn; }
    AccountType GetType() const { return m_type; }
    CString GetDescription() const { return m_description; }
    bool IsActive() const { return m_isActive; }
    CTime GetCreatedAt() const { return m_createdAt; }
    CTime GetUpdatedAt() const { return m_updatedAt; }
    const std::vector<CAccount>& GetChildren() const { return m_children; }

    // Setters
    void SetId(int id) { m_id = id; }
    void SetParentId(int parentId) { m_parentId = parentId; }
    void SetCode(const CString& code) { m_code = code; }
    void SetNameAr(const CString& nameAr) { m_nameAr = nameAr; }
    void SetNameEn(const CString& nameEn) { m_nameEn = nameEn; }
    void SetType(AccountType type) { m_type = type; }
    void SetDescription(const CString& description) { m_description = description; }
    void SetActive(bool active) { m_isActive = active; }
    void SetCreatedAt(const CTime& createdAt) { m_createdAt = createdAt; }
    void SetUpdatedAt(const CTime& updatedAt) { m_updatedAt = updatedAt; }

    // إضافة حساب فرعي
    void AddChild(const CAccount& child);
    
    // البحث عن حساب فرعي بالكود
    CAccount* FindChildByCode(const CString& code);
    
    // البحث عن حساب فرعي بالاسم
    CAccount* FindChildByName(const CString& name);
    
    // الحصول على اسم النوع بالعربية
    static CString GetTypeNameAr(AccountType type);
    
    // الحصول على اسم النوع بالإنجليزية
    static CString GetTypeNameEn(AccountType type);
    
    // تحويل النوع من نص
    static AccountType ParseType(const CString& typeStr);
    
    // التحقق من صحة الكود
    bool IsValidCode() const;
    
    // الحصول على الرصيد (سيتم تنفيذها لاحقاً)
    double GetBalance(const CTime& asOfDate = CTime::GetCurrentTime()) const;
    
    // الحصول على الرصيد المدين
    double GetDebitBalance(const CTime& asOfDate = CTime::GetCurrentTime()) const;
    
    // الحصول على الرصيد الدائن
    double GetCreditBalance(const CTime& asOfDate = CTime::GetCurrentTime()) const;
    
    // التحقق من كون الحساب نهائي (لا يحتوي على حسابات فرعية)
    bool IsLeaf() const { return m_children.empty(); }
    
    // الحصول على مستوى الحساب في الشجرة
    int GetLevel() const;
    
    // الحصول على المسار الكامل للحساب
    CString GetFullPath() const;
}; 