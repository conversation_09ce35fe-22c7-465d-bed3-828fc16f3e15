#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>
#include "resource.h"

// Global variables
HINSTANCE g_hInst;
HWND g_hMainWnd;

// Forward declarations
LRESULT CALLBACK WndProc(HWND, UINT, WPARAM, LPARAM);
INT_PTR CALLBACK AboutDlgProc(HWND, UINT, WPARAM, LPARAM);
void CreateMainMenu(HWND hWnd);
void ShowAccountsDialog();
void ShowJournalDialog();
void ShowTrialBalanceDialog();

// Window class name
const wchar_t* CLASS_NAME = L"ERPAccountingMainWindow";

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    g_hInst = hInstance;

    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_STANDARD_CLASSES;
    InitCommonControlsEx(&icex);

    // Register window class
    WNDCLASSEX wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = WndProc;
    wcex.hInstance = hInstance;
    wcex.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_ERP_ACCOUNTING));
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszClassName = CLASS_NAME;
    wcex.hIconSm = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_ERP_ACCOUNTING));

    if (!RegisterClassEx(&wcex))
    {
        MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Create main window
    g_hMainWnd = CreateWindowEx(
        0,
        CLASS_NAME,
        L"نظام ERP المحاسبي",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        1024, 768,
        nullptr,
        nullptr,
        hInstance,
        nullptr
    );

    if (!g_hMainWnd)
    {
        MessageBox(nullptr, L"Failed to create main window", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Create menu
    CreateMainMenu(g_hMainWnd);

    // Show window
    ShowWindow(g_hMainWnd, nCmdShow);
    UpdateWindow(g_hMainWnd);

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}

LRESULT CALLBACK WndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            switch (wmId)
            {
            case ID_MENU_ACCOUNTS:
                ShowAccountsDialog();
                break;
            case ID_MENU_JOURNAL_ENTRIES:
                ShowJournalDialog();
                break;
            case ID_MENU_TRIAL_BALANCE:
                ShowTrialBalanceDialog();
                break;
            case IDM_ABOUT:
                DialogBox(g_hInst, MAKEINTRESOURCE(IDD_ABOUTBOX), hWnd, AboutDlgProc);
                break;
            case IDM_EXIT:
                DestroyWindow(hWnd);
                break;
            default:
                return DefWindowProc(hWnd, message, wParam, lParam);
            }
        }
        break;

    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            
            // Set text alignment for Arabic
            SetTextAlign(hdc, TA_RIGHT | TA_TOP);
            
            // Draw welcome message
            RECT rect;
            GetClientRect(hWnd, &rect);
            
            const wchar_t* welcomeText = L"مرحباً بك في نظام ERP المحاسبي";
            TextOut(hdc, rect.right - 20, 20, welcomeText, (int)wcslen(welcomeText));
            
            const wchar_t* instructionText = L"استخدم القوائم للوصول إلى الوحدات المختلفة";
            TextOut(hdc, rect.right - 20, 50, instructionText, (int)wcslen(instructionText));
            
            EndPaint(hWnd, &ps);
        }
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

INT_PTR CALLBACK AboutDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    UNREFERENCED_PARAMETER(lParam);
    switch (message)
    {
    case WM_INITDIALOG:
        return (INT_PTR)TRUE;

    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK || LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, LOWORD(wParam));
            return (INT_PTR)TRUE;
        }
        break;
    }
    return (INT_PTR)FALSE;
}

void CreateMainMenu(HWND hWnd)
{
    HMENU hMenuBar = CreateMenu();
    
    // File menu
    HMENU hFileMenu = CreatePopupMenu();
    AppendMenu(hFileMenu, MF_STRING, IDM_EXIT, L"خروج");
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hFileMenu, L"ملف");
    
    // Accounts menu
    HMENU hAccountsMenu = CreatePopupMenu();
    AppendMenu(hAccountsMenu, MF_STRING, ID_MENU_ACCOUNTS, L"شجرة الحسابات");
    AppendMenu(hAccountsMenu, MF_STRING, ID_MENU_JOURNAL_ENTRIES, L"قيود اليومية");
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hAccountsMenu, L"الحسابات");
    
    // Reports menu
    HMENU hReportsMenu = CreatePopupMenu();
    AppendMenu(hReportsMenu, MF_STRING, ID_MENU_TRIAL_BALANCE, L"ميزان المراجعة");
    AppendMenu(hReportsMenu, MF_STRING, ID_MENU_INCOME_STATEMENT, L"قائمة الدخل");
    AppendMenu(hReportsMenu, MF_STRING, ID_MENU_BALANCE_SHEET, L"الميزانية العمومية");
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hReportsMenu, L"التقارير");
    
    // Help menu
    HMENU hHelpMenu = CreatePopupMenu();
    AppendMenu(hHelpMenu, MF_STRING, IDM_ABOUT, L"حول البرنامج");
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hHelpMenu, L"مساعدة");
    
    SetMenu(hWnd, hMenuBar);
}

void ShowAccountsDialog()
{
    MessageBox(g_hMainWnd, L"شجرة الحسابات - قيد التطوير", L"معلومات", MB_OK | MB_ICONINFORMATION);
}

void ShowJournalDialog()
{
    MessageBox(g_hMainWnd, L"قيود اليومية - قيد التطوير", L"معلومات", MB_OK | MB_ICONINFORMATION);
}

void ShowTrialBalanceDialog()
{
    MessageBox(g_hMainWnd, L"ميزان المراجعة - قيد التطوير", L"معلومات", MB_OK | MB_ICONINFORMATION);
}
