#pragma once

#include <afxwin.h>
#include <sqlite3.h>
#include <vector>
#include <map>

class CSQLiteDatabase
{
private:
    sqlite3* m_db;
    CString m_dbPath;
    bool m_isOpen;

public:
    CSQLiteDatabase();
    virtual ~CSQLiteDatabase();

    // فتح قاعدة البيانات
    bool OpenDatabase(const CString& dbPath);
    
    // إغلاق قاعدة البيانات
    void CloseDatabase();
    
    // التحقق من وجود قاعدة البيانات
    bool IsOpen() const { return m_isOpen; }
    
    // تنفيذ استعلام SQL
    bool ExecuteSQL(const CString& sql);
    
    // تنفيذ استعلام مع معاملات
    bool ExecuteSQL(const CString& sql, const std::vector<CString>& params);
    
    // استعلام SELECT
    bool Query(const CString& sql, std::vector<std::map<CString, CString>>& results);
    
    // استعلام SELECT مع معاملات
    bool Query(const CString& sql, const std::vector<CString>& params, 
               std::vector<std::map<CString, CString>>& results);
    
    // الحصول على آخر خطأ
    CString GetLastError() const;
    
    // بدء المعاملة
    bool BeginTransaction();
    
    // تأكيد المعاملة
    bool CommitTransaction();
    
    // التراجع عن المعاملة
    bool RollbackTransaction();
    
    // تهيئة قاعدة البيانات
    bool InitializeDatabase();
    
    // التحقق من وجود الجداول
    bool CheckTablesExist();
    
    // إنشاء الجداول
    bool CreateTables();
    
    // إدراج البيانات الأولية
    bool InsertInitialData();

private:
    CString m_lastError;
    
    // تحميل ملف SQL
    CString LoadSQLFile(const CString& fileName);
    
    // تنفيذ ملف SQL
    bool ExecuteSQLFile(const CString& fileName);
}; 