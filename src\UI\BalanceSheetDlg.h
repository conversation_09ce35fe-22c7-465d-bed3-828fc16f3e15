#pragma once

#include <afxwin.h>
#include <afxcmn.h>

class CBalanceSheetDlg : public CDialog
{
	DECLARE_DYNAMIC(CBalanceSheetDlg)

public:
	CBalanceSheetDlg(CWnd* pParent = nullptr);
	virtual ~CBalanceSheetDlg();

	enum { IDD = IDD_BALANCE_SHEET };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);
	DECLARE_MESSAGE_MAP()

public:
	virtual BOOL OnInitDialog();

private:
	// Controls
	CListCtrl m_listReportData;
	CButton m_btnPrint;
	CButton m_btnExport;
	CDateTimeCtrl m_dateAsOf;
	CButton m_btnGenerateReport;

	// Methods
	void InitializeControls();
	void LoadSampleData();
	void GenerateReport();
}; 