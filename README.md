# تطبيق ERP محاسبي - وحدة الحسابات

## نظرة عامة
تطبيق ERP محاسبي متكامل يعمل أوفلاين بنسبة 100%، مكتوب بلغة Visual C++ مع MFC وقاعدة بيانات SQLite.

## المتطلبات التقنية
- Visual C++ 2019 أو أحدث
- MFC (Microsoft Foundation Classes)
- SQLite (مدمج في المشروع)
- دعم اللغة العربية (RTL)
- Windows 10/11

## الوحدات المطلوبة
### المرحلة الأولى: وحدة الحسابات ✅
- [x] شجرة الحسابات
- [x] قيود اليومية (يدوية وآلية)
- [x] دفتر الأستاذ
- [x] ميزان المراجعة
- [x] قائمة الدخل
- [x] الميزانية العمومية

### المراحل القادمة
- وحدة المبيعات
- وحدة المشتريات
- وحدة المخازن
- وحدة الرواتب والأجور
- وحدة التصنيع

## المميزات
- يعمل أوفلاين بالكامل
- واجهة عربية بالكامل (RTL)
- أرقام بالإنجليزية
- ملف تثبيت واحد (setup.exe)
- لا يحتاج مكتبات خارجية

## هيكل المشروع
```
ERP_Accounting/
├── src/                    # الكود المصدري
│   ├── Database/          # طبقة قاعدة البيانات
│   ├── UI/               # واجهات المستخدم
│   ├── Models/           # نماذج البيانات
│   └── Utils/            # أدوات مساعدة
├── resources/             # الموارد (أيقونات، نصوص)
├── sql/                  # ملفات SQL
└── setup/                # ملفات التثبيت
```

## كيفية البناء والتشغيل
1. فتح المشروع في Visual Studio
2. بناء المشروع (Build)
3. تشغيل التطبيق
4. إنشاء ملف التثبيت

## قاعدة البيانات
- SQLite محلية
- ملف واحد: `erp_accounting.db`
- جداول: accounts, journal_entries, journal_lines, etc. 