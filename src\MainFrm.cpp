// MainFrm.cpp : implementation of the CMainFrame class
//

#include "stdafx.h"
#include "ERP_Accounting.h"
#include "MainFrm.h"
#include "UI/AccountsTreeDlg.h"
#include "UI/JournalEntryDlg.h"
#include "UI/TrialBalanceDlg.h"
#include "UI/IncomeStatementDlg.h"
#include "UI/BalanceSheetDlg.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CMainFrame

IMPLEMENT_DYNCREATE(CMainFrame, CFrameWnd)

BEGIN_MESSAGE_MAP(CMainFrame, CFrameWnd)
	ON_WM_CREATE()
	ON_COMMAND(ID_MENU_ACCOUNTS, &CMainFrame::OnMenuAccounts)
	ON_COMMAND(ID_MENU_JOURNAL_ENTRIES, &CMainFrame::OnMenuJournalEntries)
	ON_COMMAND(ID_MENU_LEDGER, &CMainFrame::OnMenuLedger)
	ON_COMMAND(ID_MENU_TRIAL_BALANCE, &CMainFrame::OnMenuTrialBalance)
	ON_COMMAND(ID_MENU_INCOME_STATEMENT, &CMainFrame::OnMenuIncomeStatement)
	ON_COMMAND(ID_MENU_BALANCE_SHEET, &CMainFrame::OnMenuBalanceSheet)
	ON_COMMAND(ID_MENU_SETTINGS, &CMainFrame::OnMenuSettings)
	ON_COMMAND(ID_MENU_EXIT, &CMainFrame::OnMenuExit)
END_MESSAGE_MAP()

static UINT indicators[] =
{
	ID_SEPARATOR,           // status line indicator
	ID_INDICATOR_CAPS,
	ID_INDICATOR_NUM,
	ID_INDICATOR_SCRL,
};

// CMainFrame construction/destruction

CMainFrame::CMainFrame()
{
	// TODO: add member initialization code here
}

CMainFrame::~CMainFrame()
{
}

int CMainFrame::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
	if (CFrameWnd::OnCreate(lpCreateStruct) == -1)
		return -1;

	if (!m_wndToolBar.CreateEx(this, TBSTYLE_FLAT, WS_CHILD | WS_VISIBLE | CBRS_TOP | CBRS_GRIPPER | CBRS_TOOLTIPS | CBRS_FLYBY | CBRS_SIZE_DYNAMIC) ||
		!m_wndToolBar.LoadToolBar(IDR_MAINFRAME))
	{
		TRACE0("Failed to create toolbar\n");
		return -1;      // fail to create
	}

	if (!m_wndStatusBar.Create(this))
	{
		TRACE0("Failed to create status bar\n");
		return -1;      // fail to create
	}
	m_wndStatusBar.SetIndicators(indicators, sizeof(indicators)/sizeof(UINT));

	// TODO: Delete these three lines if you don't want the toolbar to be dockable
	m_wndToolBar.EnableDocking(CBRS_ALIGN_ANY);
	EnableDocking(CBRS_ALIGN_ANY);
	DockControlBar(&m_wndToolBar);

	// Set Arabic text for status bar
	m_wndStatusBar.SetPaneText(0, _T("جاهز"));

	return 0;
}

BOOL CMainFrame::PreCreateWindow(CREATESTRUCT& cs)
{
	if( !CFrameWnd::PreCreateWindow(cs) )
		return FALSE;
	// TODO: Modify the Window class or styles here by modifying
	//  the CREATESTRUCT cs

	return TRUE;
}

// CMainFrame diagnostics

#ifdef _DEBUG
void CMainFrame::AssertValid() const
{
	CFrameWnd::AssertValid();
}

void CMainFrame::Dump(CDumpContext& dc) const
{
	CFrameWnd::Dump(dc);
}

#endif //_DEBUG

// CMainFrame message handlers

void CMainFrame::OnMenuAccounts()
{
	CAccountsTreeDlg dlg;
	dlg.DoModal();
}

void CMainFrame::OnMenuJournalEntries()
{
	CJournalEntryDlg dlg;
	dlg.DoModal();
}

void CMainFrame::OnMenuLedger()
{
	// TODO: Implement Ledger dialog
	AfxMessageBox(_T("سيتم تنفيذ دفتر الأستاذ قريباً"));
}

void CMainFrame::OnMenuTrialBalance()
{
	CTrialBalanceDlg dlg;
	dlg.DoModal();
}

void CMainFrame::OnMenuIncomeStatement()
{
	CIncomeStatementDlg dlg;
	dlg.DoModal();
}

void CMainFrame::OnMenuBalanceSheet()
{
	CBalanceSheetDlg dlg;
	dlg.DoModal();
}

void CMainFrame::OnMenuSettings()
{
	// TODO: Implement Settings dialog
	AfxMessageBox(_T("سيتم تنفيذ الإعدادات قريباً"));
}

void CMainFrame::OnMenuExit()
{
	PostQuitMessage(0);
} 