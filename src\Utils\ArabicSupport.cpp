#include "stdafx.h"
#include "ArabicSupport.h"

void CArabicSupport::InitializeArabicSupport()
{
    // Set process default layout to RTL
    SetProcessDefaultLayout(LAYOUT_RTL);
    
    // Set thread locale to Arabic
    SetThreadLocale(LOCALE_SYSTEM_DEFAULT);
}

void CArabicSupport::SetRTLText(CWnd* pWnd, const CString& text)
{
    if (pWnd)
    {
        pWnd->SetWindowText(text);
        
        // Set RTL reading order
        DWORD style = pWnd->GetStyle();
        pWnd->ModifyStyle(0, ES_RIGHT | ES_RTLREADING);
    }
}

void CArabicSupport::SetRTLWindow(CWnd* pWnd)
{
    if (pWnd)
    {
        // Set window to RTL layout
        DWORD exStyle = pWnd->GetExStyle();
        pWnd->ModifyStyleEx(0, WS_EX_LAYOUTRTL);
    }
}

CString CArabicSupport::ConvertNumbersToEnglish(const CString& text)
{
    CString result = text;
    
    // Convert Arabic numerals to English
    result.Replace(_T("٠"), _T("0"));
    result.Replace(_T("١"), _T("1"));
    result.Replace(_T("٢"), _T("2"));
    result.Replace(_T("٣"), _T("3"));
    result.Replace(_T("٤"), _T("4"));
    result.Replace(_T("٥"), _T("5"));
    result.Replace(_T("٦"), _T("6"));
    result.Replace(_T("٧"), _T("7"));
    result.Replace(_T("٨"), _T("8"));
    result.Replace(_T("٩"), _T("9"));
    
    return result;
}

CString CArabicSupport::ArabicToEnglishNumbers(const CString& text)
{
    return ConvertNumbersToEnglish(text);
}

void CArabicSupport::SetArabicFont(CWnd* pWnd, const CString& fontName)
{
    if (pWnd)
    {
        CFont* pFont = pWnd->GetFont();
        if (pFont)
        {
            LOGFONT lf;
            pFont->GetLogFont(&lf);
            _tcscpy_s(lf.lfFaceName, fontName);
            lf.lfCharSet = ARABIC_CHARSET;
            
            CFont newFont;
            newFont.CreateFontIndirect(&lf);
            pWnd->SetFont(&newFont);
        }
    }
}

CString CArabicSupport::FormatArabicDate(const CTime& date)
{
    CString result;
    result.Format(_T("%04d/%02d/%02d"), date.GetYear(), date.GetMonth(), date.GetDay());
    return result;
}

CString CArabicSupport::FormatArabicCurrency(double amount)
{
    CString result;
    result.Format(_T("%.2f ريال"), amount);
    return result;
} 