#pragma once

#include <afxwin.h>
#include <afxcmn.h>
#include <vector>
#include "../Models/Account.h"

class CAccountsTreeDlg : public CDialog
{
    DECLARE_DYNAMIC(CAccountsTreeDlg)

public:
    CAccountsTreeDlg(CWnd* pParent = nullptr);
    virtual ~CAccountsTreeDlg();

    enum { IDD = IDD_ACCOUNTS_TREE };

protected:
    virtual void DoDataExchange(CDataExchange* pDX);
    DECLARE_MESSAGE_MAP()

public:
    virtual BOOL OnInitDialog();
    afx_msg void OnBnClickedAddAccount();
    afx_msg void OnBnClickedEditAccount();
    afx_msg void OnBnClickedDeleteAccount();
    afx_msg void OnTvnSelchangedTreeAccounts(NMHDR *pNMHDR, LRESULT *pResult);
    afx_msg void OnTvnItemexpandedTreeAccounts(NMHDR *pNMHDR, LRESULT *pResult);

private:
    // Controls
    CTreeCtrl m_treeAccounts;
    CButton m_btnAddAccount;
    CButton m_btnEditAccount;
    CButton m_btnDeleteAccount;
    CEdit m_editAccountCode;
    CEdit m_editAccountName;
    CComboBox m_comboAccountType;
    CEdit m_editAccountDescription;

    // Data
    std::vector<CAccount> m_accounts;
    CAccount* m_selectedAccount;

    // Methods
    void LoadAccountsTree();
    void PopulateTree();
    void AddAccountToTree(const CAccount& account, HTREEITEM hParent = TVI_ROOT);
    void RefreshTree();
    void UpdateControls();
    void ClearControls();
    bool ValidateInput();
    bool SaveAccount();
    bool DeleteAccount();
    void LoadAccountTypes();
    
    // Database operations
    bool LoadAccountsFromDatabase();
    bool SaveAccountToDatabase(const CAccount& account);
    bool UpdateAccountInDatabase(const CAccount& account);
    bool DeleteAccountFromDatabase(int accountId);
    
    // Helper methods
    CString GetSelectedAccountCode();
    void ShowError(const CString& message);
    void ShowInfo(const CString& message);
    bool ConfirmDelete(const CString& accountName);
}; 