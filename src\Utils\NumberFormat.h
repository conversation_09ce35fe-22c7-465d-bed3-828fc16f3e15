#pragma once

#include <afxwin.h>
#include <string>

class CNumberFormat
{
public:
    // تنسيق الأرقام مع فواصل الآلاف
    static CString FormatNumber(double number, int decimalPlaces = 2);
    
    // تنسيق العملة
    static CString FormatCurrency(double amount, const CString& currency = _T("SAR"));
    
    // تحويل النص إلى رقم
    static double ParseNumber(const CString& text);
    
    // التحقق من صحة الرقم
    static bool IsValidNumber(const CString& text);
    
    // تنسيق النسبة المئوية
    static CString FormatPercentage(double value, int decimalPlaces = 2);
    
    // تنسيق الأرقام الصحيحة
    static CString FormatInteger(int number);
    
    // إزالة الفواصل من النص
    static CString RemoveCommas(const CString& text);
    
    // إضافة فواصل الآلاف
    static CString AddThousandSeparators(const CString& text);
}; 