#include "stdafx.h"
#include "SQLiteDatabase.h"
#include <sstream>

CSQLiteDatabase::CSQLiteDatabase()
    : m_pDatabase(nullptr)
{
}

CSQLiteDatabase::~CSQLiteDatabase()
{
    Close();
}

bool CSQLiteDatabase::Open(const CString& dbPath)
{
    Close(); // Close any existing connection
    
    // Convert CString to std::string
    CT2A converter(dbPath);
    std::string path(converter);
    
    int result = sqlite3_open(path.c_str(), &m_pDatabase);
    
    if (result != SQLITE_OK)
    {
        CString error;
        error.Format(_T("Cannot open database: %s"), CString(sqlite3_errmsg(m_pDatabase)));
        AfxMessageBox(error);
        sqlite3_close(m_pDatabase);
        m_pDatabase = nullptr;
        return false;
    }
    
    // Enable foreign keys
    ExecuteSQL(_T("PRAGMA foreign_keys = ON;"));
    
    return true;
}

void CSQLiteDatabase::Close()
{
    if (m_pDatabase)
    {
        sqlite3_close(m_pDatabase);
        m_pDatabase = nullptr;
    }
}

bool CSQLiteDatabase::ExecuteSQL(const CString& sql)
{
    if (!m_pDatabase)
        return false;
    
    CT2A converter(sql);
    std::string sqlStr(converter);
    
    char* errorMsg = nullptr;
    int result = sqlite3_exec(m_pDatabase, sqlStr.c_str(), nullptr, nullptr, &errorMsg);
    
    if (result != SQLITE_OK)
    {
        CString error;
        error.Format(_T("SQL Error: %s"), CString(errorMsg));
        AfxMessageBox(error);
        sqlite3_free(errorMsg);
        return false;
    }
    
    return true;
}

bool CSQLiteDatabase::ExecuteQuery(const CString& sql, std::vector<std::vector<CString>>& results)
{
    if (!m_pDatabase)
        return false;
    
    results.clear();
    
    CT2A converter(sql);
    std::string sqlStr(converter);
    
    sqlite3_stmt* stmt;
    int result = sqlite3_prepare_v2(m_pDatabase, sqlStr.c_str(), -1, &stmt, nullptr);
    
    if (result != SQLITE_OK)
    {
        CString error;
        error.Format(_T("SQL Prepare Error: %s"), CString(sqlite3_errmsg(m_pDatabase)));
        AfxMessageBox(error);
        return false;
    }
    
    int columnCount = sqlite3_column_count(stmt);
    
    while ((result = sqlite3_step(stmt)) == SQLITE_ROW)
    {
        std::vector<CString> row;
        for (int i = 0; i < columnCount; i++)
        {
            const char* text = (const char*)sqlite3_column_text(stmt, i);
            if (text)
            {
                row.push_back(CString(text));
            }
            else
            {
                row.push_back(_T(""));
            }
        }
        results.push_back(row);
    }
    
    sqlite3_finalize(stmt);
    
    if (result != SQLITE_DONE)
    {
        CString error;
        error.Format(_T("SQL Step Error: %s"), CString(sqlite3_errmsg(m_pDatabase)));
        AfxMessageBox(error);
        return false;
    }
    
    return true;
}

bool CSQLiteDatabase::InitializeDatabase()
{
    // Read and execute the database schema
    CString schemaPath = GetDatabaseSchemaPath();
    
    CStdioFile file;
    if (!file.Open(schemaPath, CFile::modeRead | CFile::typeText))
    {
        AfxMessageBox(_T("Cannot open database schema file"));
        return false;
    }
    
    CString line;
    CString sql;
    
    while (file.ReadString(line))
    {
        line.Trim();
        if (!line.IsEmpty() && !line.Left(2).Compare(_T("--")) != 0)
        {
            sql += line + _T(" ");
        }
    }
    
    file.Close();
    
    return ExecuteSQL(sql);
}

CString CSQLiteDatabase::GetDatabaseSchemaPath()
{
    CString path;
    GetModuleFileName(nullptr, path.GetBuffer(MAX_PATH), MAX_PATH);
    path.ReleaseBuffer();
    
    int pos = path.ReverseFind('\\');
    if (pos != -1)
    {
        path = path.Left(pos + 1);
    }
    
    path += _T("sql\\database_schema.sql");
    return path;
}

bool CSQLiteDatabase::IsConnected() const
{
    return m_pDatabase != nullptr;
}
