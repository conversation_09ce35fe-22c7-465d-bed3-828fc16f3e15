#pragma once

#include <afxwin.h>
#include <afxcmn.h>

class CIncomeStatementDlg : public CDialog
{
	DECLARE_DYNAMIC(CIncomeStatementDlg)

public:
	CIncomeStatementDlg(CWnd* pParent = nullptr);
	virtual ~CIncomeStatementDlg();

	enum { IDD = IDD_INCOME_STATEMENT };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);
	DECLARE_MESSAGE_MAP()

public:
	virtual BOOL OnInitDialog();

private:
	// Controls
	CListCtrl m_listReportData;
	CButton m_btnPrint;
	CButton m_btnExport;
	CDateTimeCtrl m_dateFrom;
	CDateTimeCtrl m_dateTo;
	CButton m_btnGenerateReport;

	// Methods
	void InitializeControls();
	void LoadSampleData();
	void GenerateReport();
}; 