#pragma once

#ifndef VC_EXTRALEAN
#define VC_EXTRALEAN		// استبعاد العناصر النادرة الاستخدام من Windows headers
#endif

#include "targetver.h"		// تعريفات للأهداف المدعومة

#define _ATL_CSTRING_EXPLICIT_CONSTRUCTORS	// بعض CString constructors ستكون explicit

// تحويل التحذيرات إلى أخطاء
#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif

// MFC core and standard components
#ifndef __AFXWIN_H__
	#error "include 'stdafx.h' before including this file for PCH"
#endif

#include "resource.h"		// main symbols

// MFC headers
#include <afxwin.h>         // MFC core and standard components
#include <afxext.h>         // MFC extensions
#include <afxdisp.h>        // MFC Automation classes

#ifndef _AFX_NO_AFXCMN_SUPPORT
#include <afxcmn.h>			// MFC support for Windows Common Controls
#endif // _AFX_NO_AFXCMN_SUPPORT

// SQLite headers
#include <sqlite3.h>

// Standard headers
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <sstream>
#include <iomanip>
#include <ctime>

// Arabic support
#include <windows.h>
#include <winnls.h>

// Project specific headers
#include "Utils/ArabicSupport.h"
#include "Utils/NumberFormat.h" 