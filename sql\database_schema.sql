-- =====================================================
-- مخطط قاعدة البيانات لتطبيق ERP المحاسبي
-- وحدة الحسابات - SQLite
-- =====================================================

-- جدول شجرة الحسابات
CREATE TABLE IF NOT EXISTS accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    parent_id INTEGER DEFAULT 0,
    code TEXT UNIQUE NOT NULL,
    name_ar TEXT NOT NULL,
    name_en TEXT,
    account_type TEXT NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES accounts(id)
);

-- جدول قيود اليومية
CREATE TABLE IF NOT EXISTS journal_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entry_number TEXT UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    entry_type TEXT NOT NULL CHECK (entry_type IN ('manual', 'auto')),
    reference TEXT,
    total_debit DECIMAL(15,2) DEFAULT 0,
    total_credit DECIMAL(15,2) DEFAULT 0,
    is_posted BOOLEAN DEFAULT 0,
    created_by TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- جدول تفاصيل قيود اليومية
CREATE TABLE IF NOT EXISTS journal_lines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entry_id INTEGER NOT NULL,
    account_id INTEGER NOT NULL,
    debit DECIMAL(15,2) DEFAULT 0,
    credit DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    line_number INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- إدراج البيانات الأولية
-- =====================================================

-- إدراج الحسابات الأساسية
INSERT OR IGNORE INTO accounts (id, parent_id, code, name_ar, name_en, account_type) VALUES
-- الأصول
(1, 0, '1000', 'الأصول', 'Assets', 'asset'),
(2, 1, '1100', 'الأصول المتداولة', 'Current Assets', 'asset'),
(3, 2, '1110', 'النقد وما في حكمه', 'Cash and Cash Equivalents', 'asset'),
(4, 2, '1120', 'الذمم المدينة', 'Accounts Receivable', 'asset'),
(5, 2, '1130', 'المخزون', 'Inventory', 'asset'),
(6, 1, '1200', 'الأصول الثابتة', 'Fixed Assets', 'asset'),
(7, 6, '1210', 'الأراضي', 'Land', 'asset'),
(8, 6, '1220', 'المباني', 'Buildings', 'asset'),
(9, 6, '1230', 'الآلات والمعدات', 'Machinery and Equipment', 'asset'),

-- الخصوم
(10, 0, '2000', 'الخصوم', 'Liabilities', 'liability'),
(11, 10, '2100', 'الخصوم المتداولة', 'Current Liabilities', 'liability'),
(12, 11, '2110', 'الذمم الدائنة', 'Accounts Payable', 'liability'),
(13, 11, '2120', 'أوراق الدفع', 'Notes Payable', 'liability'),
(14, 10, '2200', 'الخصوم طويلة الأجل', 'Long-term Liabilities', 'liability'),

-- حقوق الملكية
(15, 0, '3000', 'حقوق الملكية', 'Equity', 'equity'),
(16, 15, '3100', 'رأس المال', 'Capital', 'equity'),
(17, 15, '3200', 'الأرباح المحتجزة', 'Retained Earnings', 'equity'),

-- الإيرادات
(18, 0, '4000', 'الإيرادات', 'Revenues', 'revenue'),
(19, 18, '4100', 'إيرادات المبيعات', 'Sales Revenue', 'revenue'),
(20, 18, '4200', 'إيرادات أخرى', 'Other Revenues', 'revenue'),

-- المصروفات
(21, 0, '5000', 'المصروفات', 'Expenses', 'expense'),
(22, 21, '5100', 'مصروفات البيع', 'Selling Expenses', 'expense'),
(23, 21, '5200', 'مصروفات إدارية', 'Administrative Expenses', 'expense'),
(24, 21, '5300', 'مصروفات مالية', 'Financial Expenses', 'expense');

-- إدراج إعدادات النظام
INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('company_name', 'شركة تجارية', 'اسم الشركة'),
('company_address', 'العنوان', 'عنوان الشركة'),
('company_phone', '', 'هاتف الشركة'),
('fiscal_year_start', '01-01', 'بداية السنة المالية'),
('default_currency', 'SAR', 'العملة الافتراضية'),
('decimal_places', '2', 'عدد الخانات العشرية'),
('language', 'ar', 'لغة النظام');

-- إدراج مستخدم افتراضي
INSERT OR IGNORE INTO users (username, full_name, password_hash) VALUES
('admin', 'مدير النظام', 'admin123');

-- =====================================================
-- إنشاء الفهارس لتحسين الأداء
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_accounts_parent_id ON accounts(parent_id);
CREATE INDEX IF NOT EXISTS idx_accounts_code ON accounts(code);
CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX IF NOT EXISTS idx_journal_entries_number ON journal_entries(entry_number);
CREATE INDEX IF NOT EXISTS idx_journal_lines_entry_id ON journal_lines(entry_id);
CREATE INDEX IF NOT EXISTS idx_journal_lines_account_id ON journal_lines(account_id);

-- =====================================================
-- إنشاء Triggers للتحديث التلقائي
-- =====================================================

-- تحديث updated_at في جدول الحسابات
CREATE TRIGGER IF NOT EXISTS update_accounts_timestamp 
    AFTER UPDATE ON accounts
    FOR EACH ROW
BEGIN
    UPDATE accounts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- تحديث updated_at في جدول قيود اليومية
CREATE TRIGGER IF NOT EXISTS update_journal_entries_timestamp 
    AFTER UPDATE ON journal_entries
    FOR EACH ROW
BEGIN
    UPDATE journal_entries SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- تحديث updated_at في جدول إعدادات النظام
CREATE TRIGGER IF NOT EXISTS update_settings_timestamp 
    AFTER UPDATE ON system_settings
    FOR EACH ROW
BEGIN
    UPDATE system_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END; 