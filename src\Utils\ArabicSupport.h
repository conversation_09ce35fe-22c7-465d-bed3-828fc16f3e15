#pragma once

#include <afxwin.h>
#include <windows.h>

class CArabicSupport
{
public:
    // تهيئة دعم اللغة العربية
    static void InitializeArabicSupport();
    
    // تحويل النص إلى اتجاه RTL
    static void SetRTLText(CWnd* pWnd, const CString& text);
    
    // ضبط اتجاه النافذة إلى RTL
    static void SetRTLWindow(CWnd* pWnd);
    
    // تحويل الأرقام إلى الإنجليزية
    static CString ConvertNumbersToEnglish(const CString& text);
    
    // تحويل الأرقام من العربية إلى الإنجليزية
    static CString ArabicToEnglishNumbers(const CString& text);
    
    // ضبط الخط العربي
    static void SetArabicFont(CWnd* pWnd, const CString& fontName = _T("Arial"));
    
    // تحويل التاريخ إلى النمط العربي
    static CString FormatArabicDate(const CTime& date);
    
    // تحويل المبلغ إلى النمط العربي
    static CString FormatArabicCurrency(double amount);
}; 