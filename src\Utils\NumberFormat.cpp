#include "stdafx.h"
#include "NumberFormat.h"
#include <sstream>
#include <iomanip>

CString CNumberFormat::FormatNumber(double number, int decimalPlaces)
{
    CString result;
    result.Format(_T("%.*f"), decimalPlaces, number);
    
    // Add thousand separators
    return AddThousandSeparators(result);
}

CString CNumberFormat::FormatCurrency(double amount, const CString& currency)
{
    CString result = FormatNumber(amount, 2);
    result += _T(" ") + currency;
    return result;
}

double CNumberFormat::ParseNumber(const CString& text)
{
    CString cleanText = RemoveCommas(text);
    return _ttof(cleanText);
}

bool CNumberFormat::IsValidNumber(const CString& text)
{
    CString cleanText = RemoveCommas(text);
    
    // Check if it's a valid number
    TCHAR* endPtr;
    _ttof(cleanText);
    
    // If conversion failed, endPtr will point to the original string
    return endPtr != cleanText;
}

CString CNumberFormat::FormatPercentage(double value, int decimalPlaces)
{
    CString result;
    result.Format(_T("%.*f%%"), decimalPlaces, value * 100);
    return result;
}

CString CNumberFormat::FormatInteger(int number)
{
    CString result;
    result.Format(_T("%d"), number);
    return AddThousandSeparators(result);
}

CString CNumberFormat::RemoveCommas(const CString& text)
{
    CString result = text;
    result.Remove(_T(','));
    return result;
}

CString CNumberFormat::AddThousandSeparators(const CString& text)
{
    CString result = text;
    
    // Find decimal point
    int decimalPos = result.Find(_T('.'));
    if (decimalPos == -1)
        decimalPos = result.GetLength();
    
    // Add separators to integer part
    int insertPos = decimalPos - 3;
    while (insertPos > 0)
    {
        result.Insert(insertPos, _T(','));
        insertPos -= 3;
    }
    
    return result;
} 