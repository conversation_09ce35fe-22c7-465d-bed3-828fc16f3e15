#include "stdafx.h"
#include "JournalLine.h"
#include "../Database/SQLiteDatabase.h"

extern CSQLiteDatabase g_Database;

CJournalLine::CJournalLine()
    : m_nID(0)
    , m_nJournalEntryID(0)
    , m_nAccountID(0)
    , m_dDebitAmount(0.0)
    , m_dCreditAmount(0.0)
{
}

CJournalLine::~CJournalLine()
{
}

bool CJournalLine::Save()
{
    if (!g_Database.IsConnected())
        return false;

    CString strError;
    if (!Validate(strError))
    {
        AfxMessageBox(strError);
        return false;
    }

    CString sql;
    if (m_nID == 0)
    {
        // Insert new line
        sql.Format(_T("INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description) VALUES (%d, %d, %.2f, %.2f, '%s')"),
            m_nJournalEntryID,
            m_nAccountID,
            m_dDebitAmount,
            m_dCreditAmount,
            m_strDescription);
    }
    else
    {
        // Update existing line
        sql.Format(_T("UPDATE journal_lines SET journal_entry_id=%d, account_id=%d, debit_amount=%.2f, credit_amount=%.2f, description='%s' WHERE id=%d"),
            m_nJournalEntryID,
            m_nAccountID,
            m_dDebitAmount,
            m_dCreditAmount,
            m_strDescription,
            m_nID);
    }

    if (!g_Database.ExecuteSQL(sql))
        return false;

    // Get the ID if this is a new line
    if (m_nID == 0)
    {
        std::vector<std::vector<CString>> results;
        if (g_Database.ExecuteQuery(_T("SELECT last_insert_rowid()"), results) && !results.empty())
        {
            m_nID = _ttoi(results[0][0]);
        }
    }

    return true;
}

bool CJournalLine::Load(int nID)
{
    if (!g_Database.IsConnected())
        return false;

    CString sql;
    sql.Format(_T("SELECT jl.id, jl.journal_entry_id, jl.account_id, jl.debit_amount, jl.credit_amount, jl.description, a.account_code, a.account_name_ar FROM journal_lines jl LEFT JOIN accounts a ON jl.account_id = a.id WHERE jl.id=%d"), nID);

    std::vector<std::vector<CString>> results;
    if (!g_Database.ExecuteQuery(sql, results) || results.empty())
        return false;

    const auto& row = results[0];
    m_nID = _ttoi(row[0]);
    m_nJournalEntryID = _ttoi(row[1]);
    m_nAccountID = _ttoi(row[2]);
    m_dDebitAmount = _ttof(row[3]);
    m_dCreditAmount = _ttof(row[4]);
    m_strDescription = row[5];
    m_strAccountCode = row[6];
    m_strAccountName = row[7];

    return true;
}

bool CJournalLine::Delete()
{
    if (!g_Database.IsConnected() || m_nID == 0)
        return false;

    CString sql;
    sql.Format(_T("DELETE FROM journal_lines WHERE id=%d"), m_nID);

    return g_Database.ExecuteSQL(sql);
}

bool CJournalLine::LoadByJournalEntry(int nJournalEntryID, std::vector<CJournalLine>& lines)
{
    lines.clear();

    if (!g_Database.IsConnected())
        return false;

    CString sql;
    sql.Format(_T("SELECT jl.id, jl.journal_entry_id, jl.account_id, jl.debit_amount, jl.credit_amount, jl.description, a.account_code, a.account_name_ar FROM journal_lines jl LEFT JOIN accounts a ON jl.account_id = a.id WHERE jl.journal_entry_id=%d ORDER BY jl.id"), nJournalEntryID);

    std::vector<std::vector<CString>> results;
    if (!g_Database.ExecuteQuery(sql, results))
        return false;

    for (const auto& row : results)
    {
        CJournalLine line;
        line.m_nID = _ttoi(row[0]);
        line.m_nJournalEntryID = _ttoi(row[1]);
        line.m_nAccountID = _ttoi(row[2]);
        line.m_dDebitAmount = _ttof(row[3]);
        line.m_dCreditAmount = _ttof(row[4]);
        line.m_strDescription = row[5];
        line.m_strAccountCode = row[6];
        line.m_strAccountName = row[7];

        lines.push_back(line);
    }

    return true;
}

bool CJournalLine::Validate(CString& strError) const
{
    if (m_nJournalEntryID == 0)
    {
        strError = _T("معرف قيد اليومية مطلوب");
        return false;
    }

    if (m_nAccountID == 0)
    {
        strError = _T("الحساب مطلوب");
        return false;
    }

    if (m_dDebitAmount < 0 || m_dCreditAmount < 0)
    {
        strError = _T("المبالغ يجب أن تكون موجبة");
        return false;
    }

    if (m_dDebitAmount > 0 && m_dCreditAmount > 0)
    {
        strError = _T("لا يمكن أن يكون السطر مدين ودائن في نفس الوقت");
        return false;
    }

    if (m_dDebitAmount == 0 && m_dCreditAmount == 0)
    {
        strError = _T("يجب إدخال مبلغ في المدين أو الدائن");
        return false;
    }

    return true;
}
