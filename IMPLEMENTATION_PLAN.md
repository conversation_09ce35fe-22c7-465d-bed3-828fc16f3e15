# التخطيط التنفيذي - تطبيق ERP محاسبي

## 📋 **المرحلة الأولى: وحدة الحسابات**

### **المرحلة 1.1: إعداد المشروع والبنية الأساسية** ✅
- [x] إنشاء هيكل المشروع
- [x] إعداد ملفات المشروع Visual C++ مع MFC
- [x] إنشاء مخطط قاعدة البيانات SQLite
- [x] إعداد دعم اللغة العربية (RTL)
- [x] إنشاء ملفات الأدوات المساعدة

### **المرحلة 1.2: شجرة الحسابات** 🔄
- [ ] إنشاء واجهة شجرة الحسابات (AccountsTreeDlg)
- [ ] تنفيذ عمليات إضافة/تعديل/حذف الحسابات
- [ ] عرض شجرة الحسابات بشكل هرمي
- [ ] التحقق من صحة البيانات
- [ ] ربط الواجهة بقاعدة البيانات

### **المرحلة 1.3: قيود اليومية** ⏳
- [ ] إنشاء واجهة قيود اليومية (JournalEntryDlg)
- [ ] تنفيذ إدخال القيود اليدوية
- [ ] التحقق من توازن القيود (مدين = دائن)
- [ ] عرض وتعديل القيود
- [ ] ربط القيود بالحسابات

### **المرحلة 1.4: التقارير الأساسية** ⏳
- [ ] دفتر الأستاذ (Ledger)
- [ ] ميزان المراجعة (Trial Balance)
- [ ] قائمة الدخل (Income Statement)
- [ ] الميزانية العمومية (Balance Sheet)

### **المرحلة 1.5: التغليف والتوزيع** ⏳
- [ ] إنشاء ملف التثبيت (setup.exe)
- [ ] اختبار التطبيق على أجهزة مختلفة
- [ ] توثيق التطبيق

---

## 🚀 **الخطوات التالية المباشرة**

### **الخطوة 1: إكمال شجرة الحسابات**
1. تنفيذ ملف `AccountsTreeDlg.cpp`
2. تنفيذ ملف `SQLiteDatabase.cpp`
3. تنفيذ ملف `Account.cpp`
4. تنفيذ ملفات الأدوات المساعدة

### **الخطوة 2: إنشاء واجهة قيود اليومية**
1. إنشاء `JournalEntryDlg.h` و `JournalEntryDlg.cpp`
2. إنشاء نماذج `JournalEntry` و `JournalLine`
3. تنفيذ منطق إدخال القيود

### **الخطوة 3: إنشاء التقارير**
1. إنشاء واجهات التقارير
2. تنفيذ منطق حساب الأرصدة
3. إنشاء تقارير PDF أو Excel

---

## 📁 **هيكل الملفات المطلوب**

```
src/
├── stdafx.cpp                    # ملف PCH
├── ERP_Accounting.cpp            # التطبيق الرئيسي
├── MainFrm.cpp                   # الإطار الرئيسي
├── Database/
│   └── SQLiteDatabase.cpp        # طبقة قاعدة البيانات
├── Models/
│   ├── Account.cpp               # نموذج الحساب
│   ├── JournalEntry.cpp          # نموذج قيد اليومية
│   └── JournalLine.cpp           # نموذج تفصيل القيد
├── UI/
│   ├── AccountsTreeDlg.cpp       # واجهة شجرة الحسابات
│   ├── JournalEntryDlg.cpp       # واجهة قيود اليومية
│   ├── TrialBalanceDlg.cpp       # واجهة ميزان المراجعة
│   ├── IncomeStatementDlg.cpp    # واجهة قائمة الدخل
│   └── BalanceSheetDlg.cpp       # واجهة الميزانية العمومية
└── Utils/
    ├── ArabicSupport.cpp         # دعم اللغة العربية
    └── NumberFormat.cpp          # تنسيق الأرقام
```

---

## ⚙️ **المتطلبات التقنية**

### **البيئة المطلوبة:**
- Visual Studio 2019 أو أحدث
- MFC (Microsoft Foundation Classes)
- SQLite (مدمج في المشروع)
- Windows 10/11

### **المكتبات المطلوبة:**
- sqlite3.lib (مدمج)
- MFC libraries (مدمجة في Visual Studio)

### **إعدادات المشروع:**
- Character Set: Unicode
- Use of MFC: Static Library
- Platform Toolset: v142
- Windows SDK: 10.0

---

## 🎯 **الأهداف المباشرة**

1. **إكمال شجرة الحسابات** - الأسبوع الأول
2. **إكمال قيود اليومية** - الأسبوع الثاني
3. **إكمال التقارير الأساسية** - الأسبوع الثالث
4. **التغليف والاختبار** - الأسبوع الرابع

---

## 📝 **ملاحظات هامة**

- جميع النصوص باللغة العربية
- الأرقام تظهر بالإنجليزية
- التطبيق يعمل أوفلاين 100%
- لا يحتاج مكتبات خارجية
- ملف تثبيت واحد شامل

---

**هل تريد أن أبدأ بتنفيذ الخطوة التالية (شجرة الحسابات)؟** 