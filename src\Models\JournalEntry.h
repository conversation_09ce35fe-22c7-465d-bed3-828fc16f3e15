#pragma once
#include <vector>
#include "JournalLine.h"

class CJournalEntry
{
public:
    CJournalEntry();
    ~CJournalEntry();

    // Properties
    int GetID() const { return m_nID; }
    void SetID(int nID) { m_nID = nID; }

    CString GetEntryNumber() const { return m_strEntryNumber; }
    void SetEntryNumber(const CString& strNumber) { m_strEntryNumber = strNumber; }

    COleDateTime GetEntryDate() const { return m_dateEntry; }
    void SetEntryDate(const COleDateTime& date) { m_dateEntry = date; }

    CString GetDescription() const { return m_strDescription; }
    void SetDescription(const CString& strDesc) { m_strDescription = strDesc; }

    CString GetReference() const { return m_strReference; }
    void SetReference(const CString& strRef) { m_strReference = strRef; }

    double GetTotalDebit() const;
    double GetTotalCredit() const;
    bool IsBalanced() const;

    // Journal Lines
    void AddLine(const CJournalLine& line);
    void RemoveLine(int nIndex);
    void ClearLines();
    int GetLineCount() const { return (int)m_arrLines.size(); }
    CJournalLine* GetLine(int nIndex);
    const CJournalLine* GetLine(int nIndex) const;

    // Database operations
    bool Save();
    bool Load(int nID);
    bool Delete();
    static bool LoadAll(std::vector<CJournalEntry>& entries);
    static CString GenerateNextEntryNumber();

    // Validation
    bool Validate(CString& strError) const;

private:
    int m_nID;
    CString m_strEntryNumber;
    COleDateTime m_dateEntry;
    CString m_strDescription;
    CString m_strReference;
    std::vector<CJournalLine> m_arrLines;

    bool SaveLines();
    bool LoadLines();
    bool DeleteLines();
};
