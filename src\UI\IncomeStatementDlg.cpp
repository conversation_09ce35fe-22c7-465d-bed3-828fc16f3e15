#include "stdafx.h"
#include "IncomeStatementDlg.h"
#include "Utils/ArabicSupport.h"
#include "Utils/NumberFormat.h"

IMPLEMENT_DYNAMIC(CIncomeStatementDlg, CDialog)

CIncomeStatementDlg::CIncomeStatementDlg(CWnd* pParent /*=nullptr*/)
	: CDialog(CIncomeStatementDlg::IDD, pParent)
{
}

CIncomeStatementDlg::~CIncomeStatementDlg()
{
}

void CIncomeStatementDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST_REPORT_DATA, m_listReportData);
	DDX_Control(pDX, IDC_BTN_PRINT, m_btnPrint);
	DDX_Control(pDX, IDC_BTN_EXPORT, m_btnExport);
	DDX_Control(pDX, IDC_DATE_FROM, m_dateFrom);
	DDX_Control(pDX, IDC_DATE_TO, m_dateTo);
	DDX_Control(pDX, IDC_BTN_GENERATE_REPORT, m_btnGenerateReport);
}

BEGIN_MESSAGE_MAP(CIncomeStatementDlg, CDialog)
END_MESSAGE_MAP()

BOOL CIncomeStatementDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// Set RTL layout
	CArabicSupport::SetRTLWindow(this);

	// Set Arabic fonts
	CArabicSupport::SetArabicFont(this, _T("Arial"));

	// Set window title
	SetWindowText(_T("قائمة الدخل"));

	// Initialize controls
	InitializeControls();

	// Load sample data
	LoadSampleData();

	return TRUE;
}

void CIncomeStatementDlg::InitializeControls()
{
	// Set button texts
	m_btnPrint.SetWindowText(_T("طباعة"));
	m_btnExport.SetWindowText(_T("تصدير"));
	m_btnGenerateReport.SetWindowText(_T("توليد التقرير"));

	// Initialize list control
	m_listReportData.InsertColumn(0, _T("البند"), LVCFMT_LEFT, 300);
	m_listReportData.InsertColumn(1, _T("المبلغ"), LVCFMT_RIGHT, 150);

	// Set date range to current month
	CTime now = CTime::GetCurrentTime();
	CTime startOfMonth(now.GetYear(), now.GetMonth(), 1, 0, 0, 0);
	CTime endOfMonth(now.GetYear(), now.GetMonth() + 1, 1, 0, 0, 0);
	endOfMonth -= CTimeSpan(1, 0, 0, 0);

	m_dateFrom.SetTime(&startOfMonth);
	m_dateTo.SetTime(&endOfMonth);
}

void CIncomeStatementDlg::LoadSampleData()
{
	// Add sample income statement data
	int index = 0;

	// Revenue section
	m_listReportData.InsertItem(index, _T("الإيرادات:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("  إيرادات المبيعات"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(150000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  إيرادات أخرى"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(5000.00));
	index++;

	m_listReportData.InsertItem(index, _T("مجموع الإيرادات"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(155000.00));
	index++;

	// Empty row
	m_listReportData.InsertItem(index, _T(""));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	// Expenses section
	m_listReportData.InsertItem(index, _T("المصروفات:"));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	m_listReportData.InsertItem(index, _T("  مصروفات البيع"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(25000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  مصروفات إدارية"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(15000.00));
	index++;

	m_listReportData.InsertItem(index, _T("  مصروفات مالية"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(5000.00));
	index++;

	m_listReportData.InsertItem(index, _T("مجموع المصروفات"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(45000.00));
	index++;

	// Empty row
	m_listReportData.InsertItem(index, _T(""));
	m_listReportData.SetItemText(index, 1, _T(""));
	index++;

	// Net Income
	m_listReportData.InsertItem(index, _T("صافي الربح"));
	m_listReportData.SetItemText(index, 1, CNumberFormat::FormatNumber(110000.00));
}

void CIncomeStatementDlg::GenerateReport()
{
	// TODO: Implement report generation from database
	AfxMessageBox(_T("سيتم تنفيذ توليد التقرير من قاعدة البيانات قريباً"));
} 