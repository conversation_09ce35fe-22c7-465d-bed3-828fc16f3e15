#include "stdafx.h"
#include "Account.h"

CAccount::CAccount()
	: m_id(0)
	, m_parentId(0)
	, m_type(AccountType::Asset)
	, m_isActive(true)
	, m_createdAt(CTime::GetCurrentTime())
	, m_updatedAt(CTime::GetCurrentTime())
{
}

CAccount::CAccount(int id, int parentId, const CString& code, const CString& nameAr, 
				   const CString& nameEn, AccountType type, const CString& description)
	: m_id(id)
	, m_parentId(parentId)
	, m_code(code)
	, m_nameAr(nameAr)
	, m_nameEn(nameEn)
	, m_type(type)
	, m_description(description)
	, m_isActive(true)
	, m_createdAt(CTime::GetCurrentTime())
	, m_updatedAt(CTime::GetCurrentTime())
{
}

CAccount::~CAccount()
{
}

void CAccount::AddChild(const CAccount& child)
{
	m_children.push_back(child);
}

CAccount* CAccount::FindChildByCode(const CString& code)
{
	for (auto& child : m_children)
	{
		if (child.GetCode() == code)
			return &child;
	}
	return nullptr;
}

CAccount* CAccount::FindChildByName(const CString& name)
{
	for (auto& child : m_children)
	{
		if (child.GetNameAr() == name || child.GetNameEn() == name)
			return &child;
	}
	return nullptr;
}

CString CAccount::GetTypeNameAr(AccountType type)
{
	switch (type)
	{
	case AccountType::Asset:
		return _T("أصول");
	case AccountType::Liability:
		return _T("خصوم");
	case AccountType::Equity:
		return _T("حقوق ملكية");
	case AccountType::Revenue:
		return _T("إيرادات");
	case AccountType::Expense:
		return _T("مصروفات");
	default:
		return _T("غير محدد");
	}
}

CString CAccount::GetTypeNameEn(AccountType type)
{
	switch (type)
	{
	case AccountType::Asset:
		return _T("Asset");
	case AccountType::Liability:
		return _T("Liability");
	case AccountType::Equity:
		return _T("Equity");
	case AccountType::Revenue:
		return _T("Revenue");
	case AccountType::Expense:
		return _T("Expense");
	default:
		return _T("Unknown");
	}
}

AccountType CAccount::ParseType(const CString& typeStr)
{
	if (typeStr == _T("asset") || typeStr == _T("أصول"))
		return AccountType::Asset;
	else if (typeStr == _T("liability") || typeStr == _T("خصوم"))
		return AccountType::Liability;
	else if (typeStr == _T("equity") || typeStr == _T("حقوق ملكية"))
		return AccountType::Equity;
	else if (typeStr == _T("revenue") || typeStr == _T("إيرادات"))
		return AccountType::Revenue;
	else if (typeStr == _T("expense") || typeStr == _T("مصروفات"))
		return AccountType::Expense;
	else
		return AccountType::Asset; // Default
}

bool CAccount::IsValidCode() const
{
	// Check if code is not empty and contains only alphanumeric characters
	if (m_code.IsEmpty())
		return false;

	for (int i = 0; i < m_code.GetLength(); i++)
	{
		TCHAR ch = m_code[i];
		if (!_istdigit(ch) && !_istalpha(ch))
			return false;
	}

	return true;
}

double CAccount::GetBalance(const CTime& asOfDate) const
{
	// TODO: Implement balance calculation from journal entries
	// This will be implemented when we add journal entries functionality
	return 0.0;
}

double CAccount::GetDebitBalance(const CTime& asOfDate) const
{
	// TODO: Implement debit balance calculation
	return 0.0;
}

double CAccount::GetCreditBalance(const CTime& asOfDate) const
{
	// TODO: Implement credit balance calculation
	return 0.0;
}

int CAccount::GetLevel() const
{
	int level = 0;
	int currentParentId = m_parentId;
	
	// Count levels by traversing up the parent chain
	while (currentParentId != 0)
	{
		level++;
		// TODO: Find parent account and get its parent_id
		// This requires access to the accounts collection
		break; // Temporary break to avoid infinite loop
	}
	
	return level;
}

CString CAccount::GetFullPath() const
{
	CString path = m_code + _T(" - ") + m_nameAr;
	
	// TODO: Build full path by traversing up the parent chain
	// This requires access to the accounts collection
	
	return path;
} 