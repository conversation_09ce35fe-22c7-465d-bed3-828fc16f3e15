#include "stdafx.h"
#include "TrialBalanceDlg.h"
#include "Utils/ArabicSupport.h"
#include "Utils/NumberFormat.h"

IMPLEMENT_DYNAMIC(CTrialBalanceDlg, CDialog)

CTrialBalanceDlg::CTrialBalanceDlg(CWnd* pParent /*=nullptr*/)
	: CDialog(CTrialBalanceDlg::IDD, pParent)
{
}

CTrialBalanceDlg::~CTrialBalanceDlg()
{
}

void CTrialBalanceDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST_REPORT_DATA, m_listReportData);
	DDX_Control(pDX, IDC_BTN_PRINT, m_btnPrint);
	DDX_Control(pDX, IDC_BTN_EXPORT, m_btnExport);
	DDX_Control(pDX, IDC_DATE_FROM, m_dateFrom);
	DDX_Control(pDX, IDC_DATE_TO, m_dateTo);
	DDX_Control(pDX, IDC_BTN_GENERATE_REPORT, m_btnGenerateReport);
}

BEGIN_MESSAGE_MAP(CTrialBalanceDlg, CDialog)
END_MESSAGE_MAP()

BOOL CTrialBalanceDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// Set RTL layout
	CArabicSupport::SetRTLWindow(this);

	// Set Arabic fonts
	CArabicSupport::SetArabicFont(this, _T("Arial"));

	// Set window title
	SetWindowText(_T("ميزان المراجعة"));

	// Initialize controls
	InitializeControls();

	// Load sample data
	LoadSampleData();

	return TRUE;
}

void CTrialBalanceDlg::InitializeControls()
{
	// Set button texts
	m_btnPrint.SetWindowText(_T("طباعة"));
	m_btnExport.SetWindowText(_T("تصدير"));
	m_btnGenerateReport.SetWindowText(_T("توليد التقرير"));

	// Initialize list control
	m_listReportData.InsertColumn(0, _T("رقم الحساب"), LVCFMT_LEFT, 100);
	m_listReportData.InsertColumn(1, _T("اسم الحساب"), LVCFMT_LEFT, 200);
	m_listReportData.InsertColumn(2, _T("مدين"), LVCFMT_RIGHT, 120);
	m_listReportData.InsertColumn(3, _T("دائن"), LVCFMT_RIGHT, 120);
	m_listReportData.InsertColumn(4, _T("الرصيد"), LVCFMT_RIGHT, 120);

	// Set date range to current month
	CTime now = CTime::GetCurrentTime();
	CTime startOfMonth(now.GetYear(), now.GetMonth(), 1, 0, 0, 0);
	CTime endOfMonth(now.GetYear(), now.GetMonth() + 1, 1, 0, 0, 0);
	endOfMonth -= CTimeSpan(1, 0, 0, 0);

	m_dateFrom.SetTime(&startOfMonth);
	m_dateTo.SetTime(&endOfMonth);
}

void CTrialBalanceDlg::LoadSampleData()
{
	// Add sample trial balance data
	int index = 0;

	// Assets
	m_listReportData.InsertItem(index, _T("1110"));
	m_listReportData.SetItemText(index, 1, _T("النقد وما في حكمه"));
	m_listReportData.SetItemText(index, 2, CNumberFormat::FormatNumber(50000.00));
	m_listReportData.SetItemText(index, 3, _T("0.00"));
	m_listReportData.SetItemText(index, 4, CNumberFormat::FormatNumber(50000.00));
	index++;

	m_listReportData.InsertItem(index, _T("1120"));
	m_listReportData.SetItemText(index, 1, _T("الذمم المدينة"));
	m_listReportData.SetItemText(index, 2, CNumberFormat::FormatNumber(25000.00));
	m_listReportData.SetItemText(index, 3, _T("0.00"));
	m_listReportData.SetItemText(index, 4, CNumberFormat::FormatNumber(25000.00));
	index++;

	m_listReportData.InsertItem(index, _T("1130"));
	m_listReportData.SetItemText(index, 1, _T("المخزون"));
	m_listReportData.SetItemText(index, 2, CNumberFormat::FormatNumber(15000.00));
	m_listReportData.SetItemText(index, 3, _T("0.00"));
	m_listReportData.SetItemText(index, 4, CNumberFormat::FormatNumber(15000.00));
	index++;

	// Liabilities
	m_listReportData.InsertItem(index, _T("2110"));
	m_listReportData.SetItemText(index, 1, _T("الذمم الدائنة"));
	m_listReportData.SetItemText(index, 2, _T("0.00"));
	m_listReportData.SetItemText(index, 3, CNumberFormat::FormatNumber(20000.00));
	m_listReportData.SetItemText(index, 4, CNumberFormat::FormatNumber(-20000.00));
	index++;

	// Equity
	m_listReportData.InsertItem(index, _T("3100"));
	m_listReportData.SetItemText(index, 1, _T("رأس المال"));
	m_listReportData.SetItemText(index, 2, _T("0.00"));
	m_listReportData.SetItemText(index, 3, CNumberFormat::FormatNumber(70000.00));
	m_listReportData.SetItemText(index, 4, CNumberFormat::FormatNumber(-70000.00));
	index++;

	// Total row
	m_listReportData.InsertItem(index, _T(""));
	m_listReportData.SetItemText(index, 1, _T("المجموع"));
	m_listReportData.SetItemText(index, 2, CNumberFormat::FormatNumber(90000.00));
	m_listReportData.SetItemText(index, 3, CNumberFormat::FormatNumber(90000.00));
	m_listReportData.SetItemText(index, 4, _T("0.00"));
}

void CTrialBalanceDlg::GenerateReport()
{
	// TODO: Implement report generation from database
	AfxMessageBox(_T("سيتم تنفيذ توليد التقرير من قاعدة البيانات قريباً"));
} 