#pragma once

class CJournalLine
{
public:
    CJournalLine();
    ~CJournalLine();

    // Properties
    int GetID() const { return m_nID; }
    void SetID(int nID) { m_nID = nID; }

    int GetJournalEntryID() const { return m_nJournalEntryID; }
    void SetJournalEntryID(int nID) { m_nJournalEntryID = nID; }

    int GetAccountID() const { return m_nAccountID; }
    void SetAccountID(int nID) { m_nAccountID = nID; }

    CString GetAccountCode() const { return m_strAccountCode; }
    void SetAccountCode(const CString& strCode) { m_strAccountCode = strCode; }

    CString GetAccountName() const { return m_strAccountName; }
    void SetAccountName(const CString& strName) { m_strAccountName = strName; }

    double GetDebitAmount() const { return m_dDebitAmount; }
    void SetDebitAmount(double dAmount) { m_dDebitAmount = dAmount; }

    double GetCreditAmount() const { return m_dCreditAmount; }
    void SetCreditAmount(double dAmount) { m_dCreditAmount = dAmount; }

    CString GetDescription() const { return m_strDescription; }
    void SetDescription(const CString& strDesc) { m_strDescription = strDesc; }

    // Database operations
    bool Save();
    bool Load(int nID);
    bool Delete();
    static bool LoadByJournalEntry(int nJournalEntryID, std::vector<CJournalLine>& lines);

    // Validation
    bool Validate(CString& strError) const;

    // Helper methods
    bool IsDebit() const { return m_dDebitAmount > 0; }
    bool IsCredit() const { return m_dCreditAmount > 0; }
    double GetAmount() const { return IsDebit() ? m_dDebitAmount : m_dCreditAmount; }

private:
    int m_nID;
    int m_nJournalEntryID;
    int m_nAccountID;
    CString m_strAccountCode;
    CString m_strAccountName;
    double m_dDebitAmount;
    double m_dCreditAmount;
    CString m_strDescription;
};
