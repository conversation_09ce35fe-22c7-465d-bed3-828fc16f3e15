#include "stdafx.h"
// SHARED_HANDLERS can be defined in an ATL project implementing preview, thumbnail
// and search filter handlers and allows sharing of document code with that project.
#ifndef SHARED_HANDLERS
#include "ERP_Accounting.h"
#endif

#include "ERP_AccountingDoc.h"
#include "ERP_AccountingView.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


// CERPAccountingView

IMPLEMENT_DYNCREATE(CERPAccountingView, CView)

BEGIN_MESSAGE_MAP(CERPAccountingView, CView)
	// Standard printing commands
	ON_COMMAND(ID_FILE_PRINT, &CView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_DIRECT, &CView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_PREVIEW, &CERPAccountingView::OnFilePrintPreview)
	ON_WM_CONTEXTMENU()
	ON_WM_RBUTTONUP()
END_MESSAGE_MAP()

// CERPAccountingView construction/destruction

CERPAccountingView::CERPAccountingView() noexcept
{
	// TODO: add construction code here

}

CERPAccountingView::~CERPAccountingView()
{
}

BOOL CERPAccountingView::PreCreateWindow(CREATESTRUCT& cs)
{
	// TODO: Modify the Window class or styles here by modifying
	//  the CREATESTRUCT cs

	return CView::PreCreateWindow(cs);
}

// CERPAccountingView drawing

void CERPAccountingView::OnDraw(CDC* pDC)
{
	CERPAccountingDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	if (!pDoc)
		return;

	// TODO: add draw code for native data here
	CRect rect;
	GetClientRect(&rect);
	
	// Set Arabic text alignment
	pDC->SetTextAlign(TA_RIGHT | TA_TOP);
	
	// Draw welcome message in Arabic
	CString welcomeText = _T("مرحباً بك في نظام ERP المحاسبي");
	pDC->TextOut(rect.right - 20, 20, welcomeText);
	
	CString instructionText = _T("استخدم القوائم للوصول إلى الوحدات المختلفة");
	pDC->TextOut(rect.right - 20, 50, instructionText);
}


// CERPAccountingView printing


void CERPAccountingView::OnFilePrintPreview()
{
#ifndef SHARED_HANDLERS
	AFXPrintPreview(this);
#endif
}

BOOL CERPAccountingView::OnPreparePrinting(CPrintInfo* pInfo)
{
	// default preparation
	return DoPreparePrinting(pInfo);
}

void CERPAccountingView::OnBeginPrinting(CDC* /*pDC*/, CPrintInfo* /*pInfo*/)
{
	// TODO: add extra initialization before printing
}

void CERPAccountingView::OnEndPrinting(CDC* /*pDC*/, CPrintInfo* /*pInfo*/)
{
	// TODO: add cleanup after printing
}

void CERPAccountingView::OnRButtonUp(UINT /* nFlags */, CPoint point)
{
	ClientToScreen(&point);
	OnContextMenu(this, point);
}

void CERPAccountingView::OnContextMenu(CWnd* /* pWnd */, CPoint point)
{
#ifndef SHARED_HANDLERS
	theApp.GetContextMenuManager()->ShowPopupMenu(IDR_POPUP_EDIT, point.x, point.y, this, TRUE);
#endif
}


// CERPAccountingView diagnostics

#ifdef _DEBUG
void CERPAccountingView::AssertValid() const
{
	CView::AssertValid();
}

void CERPAccountingView::Dump(CDumpContext& dc) const
{
	CView::Dump(dc);
}

CERPAccountingDoc* CERPAccountingView::GetDocument() const // non-debug version is inline
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CERPAccountingDoc)));
	return (CERPAccountingDoc*)m_pDocument;
}
#endif //_DEBUG
