#include "stdafx.h"
#include "JournalEntryDlg.h"
#include "Utils/ArabicSupport.h"

IMPLEMENT_DYNAMIC(CJournalEntryDlg, CDialog)

CJournalEntryDlg::CJournalEntryDlg(CWnd* pParent /*=nullptr*/)
	: CDialog(CJournalEntryDlg::IDD, pParent)
{
}

CJournalEntryDlg::~CJournalEntryDlg()
{
}

void CJournalEntryDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST_JOURNAL_LINES, m_listJournalLines);
	DDX_Control(pDX, IDC_BTN_ADD_LINE, m_btnAddLine);
	DDX_Control(pDX, IDC_BTN_DELETE_LINE, m_btnDeleteLine);
	DDX_Control(pDX, IDC_EDIT_ENTRY_DATE, m_editEntryDate);
	DDX_Control(pDX, IDC_EDIT_ENTRY_DESCRIPTION, m_editEntryDescription);
	DDX_Control(pDX, IDC_EDIT_ENTRY_REFERENCE, m_editEntryReference);
	DDX_Control(pDX, IDC_COMBO_ACCOUNT, m_comboAccount);
	DDX_Control(pDX, IDC_EDIT_DEBIT, m_editDebit);
	DDX_Control(pDX, IDC_EDIT_CREDIT, m_editCredit);
	DDX_Control(pDX, IDC_EDIT_LINE_DESCRIPTION, m_editLineDescription);
	DDX_Control(pDX, IDC_BTN_SAVE_ENTRY, m_btnSaveEntry);
	DDX_Control(pDX, IDC_BTN_CANCEL_ENTRY, m_btnCancelEntry);
}

BEGIN_MESSAGE_MAP(CJournalEntryDlg, CDialog)
END_MESSAGE_MAP()

BOOL CJournalEntryDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// Set RTL layout
	CArabicSupport::SetRTLWindow(this);

	// Set Arabic fonts
	CArabicSupport::SetArabicFont(this, _T("Arial"));

	// Set window title
	SetWindowText(_T("قيد يومية جديد"));

	// Initialize controls
	InitializeControls();

	// Set current date
	CTime now = CTime::GetCurrentTime();
	CString dateStr;
	dateStr.Format(_T("%04d/%02d/%02d"), now.GetYear(), now.GetMonth(), now.GetDay());
	m_editEntryDate.SetWindowText(dateStr);

	return TRUE;
}

void CJournalEntryDlg::InitializeControls()
{
	// Set button texts
	m_btnAddLine.SetWindowText(_T("إضافة سطر"));
	m_btnDeleteLine.SetWindowText(_T("حذف سطر"));
	m_btnSaveEntry.SetWindowText(_T("حفظ القيد"));
	m_btnCancelEntry.SetWindowText(_T("إلغاء"));

	// Initialize list control
	m_listJournalLines.InsertColumn(0, _T("الحساب"), LVCFMT_LEFT, 150);
	m_listJournalLines.InsertColumn(1, _T("مدين"), LVCFMT_RIGHT, 100);
	m_listJournalLines.InsertColumn(2, _T("دائن"), LVCFMT_RIGHT, 100);
	m_listJournalLines.InsertColumn(3, _T("البيان"), LVCFMT_LEFT, 200);

	// Load accounts
	LoadAccounts();

	// Set default values
	m_editEntryDescription.SetWindowText(_T(""));
	m_editEntryReference.SetWindowText(_T(""));
	m_editDebit.SetWindowText(_T("0.00"));
	m_editCredit.SetWindowText(_T("0.00"));
	m_editLineDescription.SetWindowText(_T(""));
}

void CJournalEntryDlg::LoadAccounts()
{
	// Add sample accounts for testing
	m_comboAccount.AddString(_T("1110 - النقد وما في حكمه"));
	m_comboAccount.AddString(_T("1120 - الذمم المدينة"));
	m_comboAccount.AddString(_T("1130 - المخزون"));
	m_comboAccount.AddString(_T("2110 - الذمم الدائنة"));
	m_comboAccount.AddString(_T("3100 - رأس المال"));
	m_comboAccount.AddString(_T("4100 - إيرادات المبيعات"));
	m_comboAccount.AddString(_T("5100 - مصروفات البيع"));
}

void CJournalEntryDlg::AddJournalLine()
{
	// TODO: Implement add journal line functionality
	AfxMessageBox(_T("سيتم تنفيذ إضافة سطر القيد قريباً"));
}

void CJournalEntryDlg::DeleteJournalLine()
{
	// TODO: Implement delete journal line functionality
	AfxMessageBox(_T("سيتم تنفيذ حذف سطر القيد قريباً"));
}

void CJournalEntryDlg::ValidateEntry()
{
	// TODO: Implement entry validation
	// Check if debits equal credits
}

void CJournalEntryDlg::SaveEntry()
{
	// TODO: Implement save entry functionality
	AfxMessageBox(_T("سيتم تنفيذ حفظ القيد قريباً"));
} 