# دليل تجربة تطبيق ERP المحاسبي

## 🎯 **نظرة عامة**
تم إنشاء نسخة تجريبية من تطبيق ERP المحاسبي بوحدة الحسابات. هذه النسخة تحتوي على:
- واجهة عربية بالكامل (RTL)
- شجرة الحسابات مع بيانات تجريبية
- واجهات التقارير الأساسية
- دعم الأرقام الإنجليزية

## 📁 **الملفات المطلوبة للتشغيل**

### **ملفات المشروع الأساسية:**
```
src/
├── stdafx.h                    # ملف PCH الرئيسي
├── stdafx.cpp                  # تنفيذ PCH
├── targetver.h                 # إعدادات Windows
├── resource.h                  # تعريفات الموارد
├── ERP_Accounting.h            # header التطبيق الرئيسي
├── ERP_Accounting.cpp          # تنفيذ التطبيق الرئيسي
├── MainFrm.h                   # header الإطار الرئيسي
├── MainFrm.cpp                 # تنفيذ الإطار الرئيسي
├── Utils/
│   ├── ArabicSupport.h         # دعم اللغة العربية
│   ├── ArabicSupport.cpp       # تنفيذ دعم العربية
│   ├── NumberFormat.h          # تنسيق الأرقام
│   └── NumberFormat.cpp        # تنفيذ تنسيق الأرقام
├── Models/
│   ├── Account.h               # نموذج الحساب
│   └── Account.cpp             # تنفيذ نموذج الحساب
└── UI/
    ├── AccountsTreeDlg.h       # واجهة شجرة الحسابات
    ├── AccountsTreeDlg.cpp     # تنفيذ شجرة الحسابات
    ├── JournalEntryDlg.h       # واجهة قيود اليومية
    ├── JournalEntryDlg.cpp     # تنفيذ قيود اليومية
    ├── TrialBalanceDlg.h       # واجهة ميزان المراجعة
    ├── TrialBalanceDlg.cpp     # تنفيذ ميزان المراجعة
    ├── IncomeStatementDlg.h    # واجهة قائمة الدخل
    ├── IncomeStatementDlg.cpp  # تنفيذ قائمة الدخل
    ├── BalanceSheetDlg.h       # واجهة الميزانية العمومية
    └── BalanceSheetDlg.cpp     # تنفيذ الميزانية العمومية
```

## 🚀 **كيفية تجربة التطبيق**

### **الخطوة 1: إعداد Visual Studio**
1. افتح Visual Studio 2019 أو أحدث
2. أنشئ مشروع MFC جديد
3. انسخ الملفات المذكورة أعلاه إلى المشروع
4. تأكد من إعدادات المشروع:
   - Character Set: Unicode
   - Use of MFC: Static Library
   - Platform Toolset: v142

### **الخطوة 2: إضافة الملفات للمشروع**
1. أضف جميع ملفات `.h` و `.cpp` إلى المشروع
2. تأكد من أن `stdafx.h` يحتوي على جميع الـ includes المطلوبة
3. تأكد من أن `resource.h` يحتوي على جميع تعريفات الـ IDs

### **الخطوة 3: بناء المشروع**
1. اضغط `Ctrl+Shift+B` لبناء المشروع
2. تأكد من عدم وجود أخطاء في البناء
3. اضغط `F5` لتشغيل التطبيق

## 🎮 **تجربة الوظائف**

### **1. شجرة الحسابات**
- انقر على "شجرة الحسابات" من القائمة
- ستظهر شجرة الحسابات مع بيانات تجريبية
- يمكنك:
  - تصفح الحسابات في الشجرة
  - اختيار حساب لعرض تفاصيله
  - تجربة أزرار الإضافة والتعديل والحذف

### **2. قيود اليومية**
- انقر على "قيود اليومية" من القائمة
- ستظهر واجهة إدخال قيد جديد
- يمكنك تجربة:
  - إدخال تاريخ القيد
  - اختيار الحسابات
  - إدخال المبالغ

### **3. التقارير**
- **ميزان المراجعة**: يعرض أرصدة الحسابات
- **قائمة الدخل**: يعرض الإيرادات والمصروفات
- **الميزانية العمومية**: يعرض الأصول والخصوم وحقوق الملكية

## 📊 **البيانات التجريبية المضمنة**

### **شجرة الحسابات:**
- الأصول (1000)
  - الأصول المتداولة (1100)
    - النقد وما في حكمه (1110)
    - الذمم المدينة (1120)
    - المخزون (1130)
- الخصوم (2000)
  - الخصوم المتداولة (2100)
    - الذمم الدائنة (2110)
- حقوق الملكية (3000)
  - رأس المال (3100)
- الإيرادات (4000)
  - إيرادات المبيعات (4100)
- المصروفات (5000)
  - مصروفات البيع (5100)

### **التقارير التجريبية:**
- **ميزان المراجعة**: أرصدة تجريبية للحسابات
- **قائمة الدخل**: إيرادات ومصروفات تجريبية
- **الميزانية العمومية**: أصول وخصوم وحقوق ملكية تجريبية

## ⚠️ **ملاحظات هامة**

### **ما يعمل حالياً:**
- ✅ واجهة عربية بالكامل
- ✅ شجرة الحسابات مع بيانات تجريبية
- ✅ واجهات التقارير مع بيانات تجريبية
- ✅ دعم الأرقام الإنجليزية
- ✅ تنسيق العملة والأرقام

### **ما يحتاج تطوير:**
- 🔄 قاعدة البيانات SQLite (حالياً تستخدم بيانات تجريبية)
- 🔄 حفظ وتحميل البيانات الفعلية
- 🔄 التحقق من صحة البيانات
- 🔄 طباعة التقارير
- 🔄 تصدير البيانات

## 🛠️ **التطوير المستقبلي**

### **المرحلة التالية:**
1. **إضافة قاعدة البيانات SQLite**
2. **ربط الواجهات بقاعدة البيانات**
3. **إضافة التحقق من صحة البيانات**
4. **إضافة طباعة التقارير**
5. **إضافة تصدير البيانات**

### **المراحل القادمة:**
- وحدة المبيعات
- وحدة المشتريات
- وحدة المخازن
- وحدة الرواتب والأجور
- وحدة التصنيع

## 📞 **الدعم والمساعدة**

إذا واجهت أي مشاكل في التجربة:
1. تأكد من إعدادات Visual Studio
2. تأكد من إضافة جميع الملفات للمشروع
3. تأكد من عدم وجود أخطاء في البناء
4. راجع ملفات الـ header للتأكد من الـ includes

---

**استمتع بتجربة التطبيق! 🎉** 