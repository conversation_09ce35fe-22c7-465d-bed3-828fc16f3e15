#pragma once

#include <afxwin.h>
#include <afxcmn.h>

class CJournalEntryDlg : public CDialog
{
	DECLARE_DYNAMIC(CJournalEntryDlg)

public:
	CJournalEntryDlg(CWnd* pParent = nullptr);
	virtual ~CJournalEntryDlg();

	enum { IDD = IDD_JOURNAL_ENTRY };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);
	DECLARE_MESSAGE_MAP()

public:
	virtual BOOL OnInitDialog();

private:
	// Controls
	CListCtrl m_listJournalLines;
	CButton m_btnAddLine;
	CButton m_btnDeleteLine;
	CEdit m_editEntryDate;
	CEdit m_editEntryDescription;
	CEdit m_editEntryReference;
	CComboBox m_comboAccount;
	CEdit m_editDebit;
	CEdit m_editCredit;
	CEdit m_editLineDescription;
	CButton m_btnSaveEntry;
	CButton m_btnCancelEntry;

	// Methods
	void InitializeControls();
	void LoadAccounts();
	void AddJournalLine();
	void DeleteJournalLine();
	void ValidateEntry();
	void SaveEntry();
}; 