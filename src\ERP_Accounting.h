#pragma once

#ifndef __AFXWIN_H__
	#error "include 'stdafx.h' before including this file for PCH"
#endif

#include "resource.h"		// main symbols

// CERPAccountingApp:
// See ERP_Accounting.cpp for the implementation of this class
//

class CERPAccountingApp : public CWinApp
{
public:
	CERPAccountingApp();

// Overrides
public:
	virtual BOOL InitInstance();

// Implementation
	afx_msg void OnAppAbout();
	DECLARE_MESSAGE_MAP()
};

extern CERPAccountingApp theApp; 