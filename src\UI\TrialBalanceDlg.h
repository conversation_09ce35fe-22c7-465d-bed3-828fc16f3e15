#pragma once

#include <afxwin.h>
#include <afxcmn.h>

class CTrialBalanceDlg : public CDialog
{
	DECLARE_DYNAMIC(CTrialBalanceDlg)

public:
	CTrialBalanceDlg(CWnd* pParent = nullptr);
	virtual ~CTrialBalanceDlg();

	enum { IDD = IDD_TRIAL_BALANCE };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);
	DECLARE_MESSAGE_MAP()

public:
	virtual BOOL OnInitDialog();

private:
	// Controls
	CListCtrl m_listReportData;
	CButton m_btnPrint;
	CButton m_btnExport;
	CDateTimeCtrl m_dateFrom;
	CDateTimeCtrl m_dateTo;
	CButton m_btnGenerateReport;

	// Methods
	void InitializeControls();
	void LoadSampleData();
	void GenerateReport();
}; 