#include "stdafx.h"
#include "JournalEntry.h"
#include "../Database/SQLiteDatabase.h"

extern CSQLiteDatabase g_Database;

CJournalEntry::CJournalEntry()
    : m_nID(0)
    , m_dateEntry(COleDateTime::GetCurrentTime())
{
}

CJournalEntry::~CJournalEntry()
{
}

double CJournalEntry::GetTotalDebit() const
{
    double total = 0.0;
    for (const auto& line : m_arrLines)
    {
        total += line.GetDebitAmount();
    }
    return total;
}

double CJournalEntry::GetTotalCredit() const
{
    double total = 0.0;
    for (const auto& line : m_arrLines)
    {
        total += line.GetCreditAmount();
    }
    return total;
}

bool CJournalEntry::IsBalanced() const
{
    return abs(GetTotalDebit() - GetTotalCredit()) < 0.01; // Allow for small rounding errors
}

void CJournalEntry::AddLine(const CJournalLine& line)
{
    m_arrLines.push_back(line);
}

void CJournalEntry::RemoveLine(int nIndex)
{
    if (nIndex >= 0 && nIndex < (int)m_arrLines.size())
    {
        m_arrLines.erase(m_arrLines.begin() + nIndex);
    }
}

void CJournalEntry::ClearLines()
{
    m_arrLines.clear();
}

CJournalLine* CJournalEntry::GetLine(int nIndex)
{
    if (nIndex >= 0 && nIndex < (int)m_arrLines.size())
    {
        return &m_arrLines[nIndex];
    }
    return nullptr;
}

const CJournalLine* CJournalEntry::GetLine(int nIndex) const
{
    if (nIndex >= 0 && nIndex < (int)m_arrLines.size())
    {
        return &m_arrLines[nIndex];
    }
    return nullptr;
}

bool CJournalEntry::Save()
{
    if (!g_Database.IsConnected())
        return false;

    CString strError;
    if (!Validate(strError))
    {
        AfxMessageBox(strError);
        return false;
    }

    CString sql;
    if (m_nID == 0)
    {
        // Insert new entry
        sql.Format(_T("INSERT INTO journal_entries (entry_number, entry_date, description, reference) VALUES ('%s', '%s', '%s', '%s')"),
            m_strEntryNumber,
            m_dateEntry.Format(_T("%Y-%m-%d")),
            m_strDescription,
            m_strReference);
    }
    else
    {
        // Update existing entry
        sql.Format(_T("UPDATE journal_entries SET entry_number='%s', entry_date='%s', description='%s', reference='%s' WHERE id=%d"),
            m_strEntryNumber,
            m_dateEntry.Format(_T("%Y-%m-%d")),
            m_strDescription,
            m_strReference,
            m_nID);
    }

    if (!g_Database.ExecuteSQL(sql))
        return false;

    // Get the ID if this is a new entry
    if (m_nID == 0)
    {
        std::vector<std::vector<CString>> results;
        if (g_Database.ExecuteQuery(_T("SELECT last_insert_rowid()"), results) && !results.empty())
        {
            m_nID = _ttoi(results[0][0]);
        }
    }

    // Save lines
    return SaveLines();
}

bool CJournalEntry::Load(int nID)
{
    if (!g_Database.IsConnected())
        return false;

    CString sql;
    sql.Format(_T("SELECT id, entry_number, entry_date, description, reference FROM journal_entries WHERE id=%d"), nID);

    std::vector<std::vector<CString>> results;
    if (!g_Database.ExecuteQuery(sql, results) || results.empty())
        return false;

    const auto& row = results[0];
    m_nID = _ttoi(row[0]);
    m_strEntryNumber = row[1];
    m_dateEntry.ParseDateTime(row[2]);
    m_strDescription = row[3];
    m_strReference = row[4];

    return LoadLines();
}

bool CJournalEntry::Delete()
{
    if (!g_Database.IsConnected() || m_nID == 0)
        return false;

    // Delete lines first
    if (!DeleteLines())
        return false;

    CString sql;
    sql.Format(_T("DELETE FROM journal_entries WHERE id=%d"), m_nID);

    return g_Database.ExecuteSQL(sql);
}

bool CJournalEntry::LoadAll(std::vector<CJournalEntry>& entries)
{
    entries.clear();

    if (!g_Database.IsConnected())
        return false;

    CString sql = _T("SELECT id FROM journal_entries ORDER BY entry_date DESC, entry_number DESC");
    std::vector<std::vector<CString>> results;

    if (!g_Database.ExecuteQuery(sql, results))
        return false;

    for (const auto& row : results)
    {
        CJournalEntry entry;
        if (entry.Load(_ttoi(row[0])))
        {
            entries.push_back(entry);
        }
    }

    return true;
}

CString CJournalEntry::GenerateNextEntryNumber()
{
    if (!g_Database.IsConnected())
        return _T("JE001");

    CString sql = _T("SELECT MAX(CAST(SUBSTR(entry_number, 3) AS INTEGER)) FROM journal_entries WHERE entry_number LIKE 'JE%'");
    std::vector<std::vector<CString>> results;

    if (g_Database.ExecuteQuery(sql, results) && !results.empty() && !results[0][0].IsEmpty())
    {
        int nextNum = _ttoi(results[0][0]) + 1;
        CString strNumber;
        strNumber.Format(_T("JE%03d"), nextNum);
        return strNumber;
    }

    return _T("JE001");
}

bool CJournalEntry::Validate(CString& strError) const
{
    if (m_strEntryNumber.IsEmpty())
    {
        strError = _T("رقم القيد مطلوب");
        return false;
    }

    if (m_strDescription.IsEmpty())
    {
        strError = _T("وصف القيد مطلوب");
        return false;
    }

    if (m_arrLines.size() < 2)
    {
        strError = _T("يجب أن يحتوي القيد على سطرين على الأقل");
        return false;
    }

    if (!IsBalanced())
    {
        strError = _T("القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن");
        return false;
    }

    return true;
}

bool CJournalEntry::SaveLines()
{
    if (!DeleteLines())
        return false;

    for (auto& line : m_arrLines)
    {
        line.SetJournalEntryID(m_nID);
        if (!line.Save())
            return false;
    }

    return true;
}

bool CJournalEntry::LoadLines()
{
    m_arrLines.clear();
    return CJournalLine::LoadByJournalEntry(m_nID, m_arrLines);
}

bool CJournalEntry::DeleteLines()
{
    if (m_nID == 0)
        return true;

    CString sql;
    sql.Format(_T("DELETE FROM journal_lines WHERE journal_entry_id=%d"), m_nID);
    return g_Database.ExecuteSQL(sql);
}
